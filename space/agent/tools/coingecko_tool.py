"""
CoinGecko API 工具 - 用于获取加密货币价格和市场数据
"""

from typing import Dict, Any, Optional
try:
    from llama_index.tools import BaseTool
except ImportError:
    # 备用基类
    class BaseTool:
        def __init__(self, name, description):
            self.name = name
            self.description = description

        def call(self, **kwargs):
            raise NotImplementedError
from modules.price_tracker import PriceTracker
import json


class CoinGeckoTool(BaseTool):
    """CoinGecko API 工具类"""
    
    def __init__(self):
        self.price_tracker = PriceTracker()
        super().__init__(
            name="coingecko_tool",
            description="""
            获取加密货币价格和市场数据的工具。
            
            功能包括：
            1. 搜索币种信息
            2. 获取当前价格
            3. 计算价格影响
            4. 获取历史价格数据
            
            输入参数：
            - action: 操作类型 (search_coin, get_price, calculate_impact)
            - coin_query: 币种名称或符号
            - coin_id: CoinGecko币种ID (可选)
            - timestamp: 时间戳 (用于价格影响计算)
            """
        )
    
    def call(self, **kwargs) -> str:
        """执行CoinGecko工具调用"""
        try:
            action = kwargs.get('action', 'search_coin')
            coin_query = kwargs.get('coin_query', '')
            coin_id = kwargs.get('coin_id', '')
            timestamp = kwargs.get('timestamp', None)
            
            if action == 'search_coin':
                return self._search_coin(coin_query)
            elif action == 'get_price':
                return self._get_current_price(coin_id or coin_query)
            elif action == 'calculate_impact':
                return self._calculate_price_impact(coin_id, timestamp)
            else:
                return json.dumps({
                    "error": f"不支持的操作: {action}",
                    "supported_actions": ["search_coin", "get_price", "calculate_impact"]
                })
                
        except Exception as e:
            return json.dumps({"error": f"CoinGecko工具执行失败: {str(e)}"})
    
    def _search_coin(self, coin_query: str) -> str:
        """搜索币种信息"""
        if not coin_query:
            return json.dumps({"error": "请提供币种名称或符号"})
        
        coin_info = self.price_tracker.search_coin(coin_query)
        if coin_info:
            return json.dumps({
                "success": True,
                "coin_info": coin_info,
                "message": f"找到币种: {coin_info['name']} ({coin_info['symbol'].upper()})"
            })
        else:
            return json.dumps({
                "success": False,
                "error": f"未找到币种: {coin_query}"
            })
    
    def _get_current_price(self, coin_identifier: str) -> str:
        """获取当前价格"""
        if not coin_identifier:
            return json.dumps({"error": "请提供币种ID或名称"})
        
        # 如果是名称，先搜索获取ID
        if not coin_identifier.replace('-', '').replace('_', '').isalnum():
            coin_info = self.price_tracker.search_coin(coin_identifier)
            if not coin_info:
                return json.dumps({"error": f"未找到币种: {coin_identifier}"})
            coin_identifier = coin_info['id']
        
        price_data = self.price_tracker.get_current_price(coin_identifier)
        if price_data:
            return json.dumps({
                "success": True,
                "price_data": price_data,
                "message": f"获取到 {coin_identifier} 的当前价格数据"
            })
        else:
            return json.dumps({
                "success": False,
                "error": f"无法获取 {coin_identifier} 的价格数据"
            })
    
    def _calculate_price_impact(self, coin_id: str, timestamp: str) -> str:
        """计算价格影响"""
        if not coin_id or not timestamp:
            return json.dumps({"error": "请提供币种ID和时间戳"})
        
        try:
            from datetime import datetime
            tweet_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            
            impacts = self.price_tracker.calculate_price_impact(coin_id, tweet_time)
            return json.dumps({
                "success": True,
                "price_impacts": impacts,
                "message": f"计算了 {coin_id} 在 {timestamp} 的价格影响"
            })
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"价格影响计算失败: {str(e)}"
            })
