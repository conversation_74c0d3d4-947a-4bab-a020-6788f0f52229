"""
部署到 Hugging Face Space 的脚本
"""

import os
import shutil
import json
from pathlib import Path


def create_space_structure():
    """创建 Hugging Face Space 的文件结构"""
    
    # 创建 space 目录
    space_dir = Path("space")
    if space_dir.exists():
        shutil.rmtree(space_dir)
    space_dir.mkdir()
    
    print("📁 创建 Space 目录结构...")
    
    # 复制核心文件
    files_to_copy = [
        # 主应用文件
        ("simple_agent_app.py", "app.py"),  # 重命名为 app.py
        
        # 配置文件
        ("config.py", "config.py"),
        ("requirements.txt", "requirements.txt"),
        
        # 模块目录
        ("modules/", "modules/"),
        ("utils/", "utils/"),
        ("data/", "data/"),
        
        # Agent 相关（可选）
        ("agent/", "agent/"),
        ("config/", "config/"),
        
        # README
        ("README_SPACE.md", "README.md"),
    ]
    
    for src, dst in files_to_copy:
        src_path = Path(src)
        dst_path = space_dir / dst
        
        if src_path.exists():
            if src_path.is_dir():
                shutil.copytree(src_path, dst_path)
                print(f"✅ 复制目录: {src} -> {dst}")
            else:
                dst_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(src_path, dst_path)
                print(f"✅ 复制文件: {src} -> {dst}")
        else:
            print(f"⚠️  文件不存在: {src}")
    
    # 创建 .env.example 文件
    env_example = space_dir / ".env.example"
    env_example.write_text("""
# Hugging Face API Token (可选)
HUGGINGFACE_API_TOKEN=your_token_here

# 其他配置
DEBUG=False
""")
    print("✅ 创建 .env.example")
    
    # 创建 .gitignore
    gitignore = space_dir / ".gitignore"
    gitignore.write_text("""
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/
.env
.DS_Store
*.log
data/cache/
""")
    print("✅ 创建 .gitignore")
    
    print(f"\n🎉 Space 文件结构创建完成！目录: {space_dir.absolute()}")
    return space_dir


def optimize_for_space(space_dir: Path):
    """优化 Space 配置"""
    
    print("\n🔧 优化 Space 配置...")
    
    # 优化 requirements.txt - 移除可能有问题的依赖
    req_file = space_dir / "requirements.txt"
    if req_file.exists():
        with open(req_file, 'r') as f:
            lines = f.readlines()
        
        # 过滤掉可能有问题的依赖
        filtered_lines = []
        skip_packages = ['llama-index', 'chromadb', 'torch']
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#'):
                package_name = line.split('==')[0].split('>=')[0].split('<=')[0]
                if not any(skip in package_name for skip in skip_packages):
                    filtered_lines.append(line)
                else:
                    print(f"⚠️  跳过可能有问题的包: {package_name}")
            elif line:
                filtered_lines.append(line)
        
        # 添加必要的基础依赖
        essential_deps = [
            "gradio==4.44.0",
            "pandas==2.1.4",
            "numpy==1.24.3",
            "requests==2.31.0",
            "plotly==5.17.0",
            "pycoingecko==3.1.0",
            "snscrape==0.7.0.20230622",
            "python-dateutil==2.8.2",
            "python-dotenv==1.0.0",
            "diskcache==5.6.3",
            "transformers==4.36.2",
            "huggingface-hub==0.19.4"
        ]
        
        # 重写 requirements.txt
        with open(req_file, 'w') as f:
            f.write("# Hugging Face Space 优化版依赖\n\n")
            for dep in essential_deps:
                f.write(f"{dep}\n")
        
        print("✅ 优化 requirements.txt")
    
    # 修改 app.py 确保使用简化版
    app_file = space_dir / "app.py"
    if app_file.exists():
        content = app_file.read_text()
        
        # 确保导入正确
        if "from agent import CryptoKOLAgent" in content:
            content = content.replace(
                "from agent import CryptoKOLAgent",
                "# from agent import CryptoKOLAgent  # 使用简化版"
            )
        
        app_file.write_text(content)
        print("✅ 优化 app.py")


def create_deployment_guide(space_dir: Path):
    """创建部署指南"""
    
    guide_file = space_dir / "DEPLOYMENT.md"
    guide_content = """
# 🚀 Hugging Face Space 部署指南

## 📋 部署步骤

### 1. 创建 Hugging Face Space
1. 访问 https://huggingface.co/spaces
2. 点击 "Create new Space"
3. 填写信息：
   - Space name: `shillrank-agent`
   - License: `MIT`
   - SDK: `Gradio`
   - Hardware: `CPU basic` (免费)

### 2. 上传文件
将 `space/` 目录下的所有文件上传到你的 Space 仓库：

```bash
git clone https://huggingface.co/spaces/YOUR_USERNAME/shillrank-agent
cd shillrank-agent
cp -r ../space/* .
git add .
git commit -m "Initial commit: ShillRank Agent"
git push
```

### 3. 配置环境变量（可选）
在 Space 设置中添加：
- `HUGGINGFACE_API_TOKEN`: 你的 HF API Token

### 4. 等待构建
Space 会自动构建和部署，通常需要 5-10 分钟。

## 🔧 故障排除

### 常见问题
1. **依赖安装失败**: 检查 requirements.txt 中的包版本
2. **内存不足**: 考虑升级到付费硬件
3. **API 限制**: 确保 API Token 有效

### 优化建议
1. 使用缓存减少 API 调用
2. 限制并发用户数
3. 优化模型加载

## 📊 监控
- 查看 Space 日志了解运行状态
- 监控 API 使用量
- 收集用户反馈

## 🎯 竞赛提交
确保 Space 包含：
- ✅ 完整的功能演示
- ✅ 清晰的使用说明
- ✅ 技术架构说明
- ✅ 开源代码
"""
    
    guide_file.write_text(guide_content)
    print("✅ 创建部署指南")


def main():
    """主函数"""
    print("🚀 开始准备 Hugging Face Space 部署...")
    
    # 创建 Space 结构
    space_dir = create_space_structure()
    
    # 优化配置
    optimize_for_space(space_dir)
    
    # 创建部署指南
    create_deployment_guide(space_dir)
    
    print(f"""
🎉 部署准备完成！

📁 Space 文件位置: {space_dir.absolute()}

📋 下一步：
1. 检查 space/ 目录中的文件
2. 测试本地运行: cd space && python app.py
3. 按照 DEPLOYMENT.md 指南部署到 Hugging Face

🏆 竞赛提交清单：
✅ Agent 功能完整
✅ 对话界面友好
✅ 技术文档齐全
✅ 代码开源可用
✅ 部署到 HF Space

祝你在 Agent Demo 马拉松中取得好成绩！🏆
""")


if __name__ == "__main__":
    main()
