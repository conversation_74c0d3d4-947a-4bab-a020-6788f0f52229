# 🔧 ShillRank Agent - Technical Specifications

## 📋 System Overview

**ShillRank Agent** is a conversational AI system designed for cryptocurrency KOL influence analysis, built with modern Python technologies and deployed on Hugging Face Spaces.

## 🏗️ Architecture Components

### Core System Architecture
```
┌─────────────────────────────────────────┐
│            Frontend Layer               │
│         Gradio ChatInterface            │
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│           Application Layer             │
│    ┌─────────────┐ ┌─────────────────┐  │
│    │   Natural   │ │   Conversation  │  │
│    │  Language   │ │    Manager      │  │
│    │ Processor   │ │                 │  │
│    └─────────────┘ └─────────────────┘  │
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│            Analysis Engine              │
│  ┌─────────────┐ ┌─────────────────────┐│
│  │   Data      │ │    KOL Influence    ││
│  │ Collector   │ │   Analyzer          ││
│  └─────────────┘ └─────────────────────┘│
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│           Data Sources Layer            │
│  ┌─────────────┐ ┌─────────────────────┐│
│  │  CoinGecko  │ │   Social Media      ││
│  │     API     │ │      APIs           ││
│  └─────────────┘ └─────────────────────┘│
└─────────────────────────────────────────┘
```

## 💻 Technology Stack

### Backend Technologies
- **Python 3.8+**: Core programming language
- **Gradio 4.44.0**: Web interface framework
- **Pandas 2.1.4**: Data manipulation and analysis
- **NumPy 1.24.3**: Numerical computing
- **Requests 2.31.0**: HTTP library for API calls

### AI/ML Components
- **Hugging Face Transformers 4.36.2**: NLP model integration
- **Hugging Face Hub 0.19.4**: Model and dataset access
- **Natural Language Processing**: Intent recognition and response generation

### Data Visualization
- **Plotly 5.17.0**: Interactive charts and graphs
- **Matplotlib 3.8.2**: Statistical plotting (backup)

### Data Sources
- **CoinGecko API**: Cryptocurrency market data
- **pycoingecko 3.1.0**: Python wrapper for CoinGecko
- **snscrape 0.7.0**: Social media data collection

### Utilities
- **python-dateutil 2.8.2**: Date/time processing
- **python-dotenv 1.0.0**: Environment variable management
- **diskcache 5.6.3**: Persistent caching system
- **tqdm 4.66.1**: Progress bars for long operations

## 🔧 Core Modules

### 1. Conversation Manager (`SimpleAgent`)
```python
class SimpleAgent:
    - chat(message: str) -> tuple[str, chart, table]
    - _process_message(message: str) -> response
    - _extract_coin_name(message: str) -> str
    - _extract_days(message: str) -> int
```

### 2. Analysis Engine (`ShillRankAnalyzer`)
```python
class ShillRankAnalyzer:
    - analyze_coin_influence(coin_query: str, days: int) -> dict
    - get_kol_detail(username: str, coin_query: str, days: int) -> dict
    - _calculate_rankings(kol_results: list) -> list
```

### 3. Data Collection (`KOLScraper`, `PriceTracker`)
```python
class KOLScraper:
    - analyze_kol_mentions(username: str, coin_symbol: str, coin_name: str, days: int) -> dict
    - get_user_tweets(username: str, days: int) -> list

class PriceTracker:
    - search_coin(coin_query: str) -> dict
    - get_current_price(coin_id: str) -> dict
    - calculate_price_impact(coin_id: str, tweet_time: datetime) -> dict
```

### 4. AI Analysis (`LLMAnalyzer`)
```python
class LLMAnalyzer:
    - analyze_sentiment(text: str) -> dict
    - generate_summary(kol_data: list, coin_symbol: str) -> str
    - analyze_tweet_sentiment_batch(tweets: list) -> list
```

## 📊 Data Processing Pipeline

### 1. Input Processing
```
User Query → Intent Recognition → Parameter Extraction → Validation
```

### 2. Data Collection
```
Coin Search → KOL Identification → Tweet Scraping → Price Data Retrieval
```

### 3. Analysis Processing
```
Sentiment Analysis → Engagement Calculation → Price Impact Analysis → Ranking Generation
```

### 4. Response Generation
```
Text Summary → Chart Creation → Table Formatting → Response Compilation
```

## 🎯 Algorithm Specifications

### KOL Influence Scoring Algorithm
```python
SCORE_WEIGHTS = {
    "price_impact": 0.4,    # 40% - Price correlation analysis
    "interactions": 0.3,    # 30% - Social media engagement
    "frequency": 0.2,       # 20% - Mention frequency
    "sentiment": 0.1        # 10% - Content sentiment
}

total_score = (
    price_score * 0.4 +
    interaction_score * 0.3 +
    frequency_score * 0.2 +
    sentiment_score * 0.1
)
```

### Price Impact Calculation
```python
def calculate_price_impact(coin_id: str, tweet_time: datetime) -> dict:
    # Analyze price changes at multiple time intervals
    intervals = [1, 4, 24, 72]  # hours
    impacts = {}
    
    for interval in intervals:
        before_price = get_price_at_time(coin_id, tweet_time)
        after_price = get_price_at_time(coin_id, tweet_time + timedelta(hours=interval))
        impact = ((after_price - before_price) / before_price) * 100
        impacts[f"{interval}h"] = impact
    
    return impacts
```

## 🔒 Security and Performance

### Security Measures
- **API Key Management**: Secure environment variable handling
- **Input Validation**: Sanitization of user inputs
- **Rate Limiting**: API call throttling to prevent abuse
- **Error Handling**: Comprehensive exception management

### Performance Optimizations
- **Caching System**: DiskCache for API response caching
- **Asynchronous Processing**: Non-blocking data collection
- **Memory Management**: Efficient data structure usage
- **Response Time**: Target <10 seconds for standard queries

### Scalability Features
- **Modular Architecture**: Easy component replacement and expansion
- **Configuration Management**: Environment-based settings
- **Resource Monitoring**: Built-in performance tracking
- **Graceful Degradation**: Fallback mechanisms for service failures

## 🚀 Deployment Specifications

### Hugging Face Spaces Configuration
```yaml
title: ShillRank Agent
emoji: 🤖
colorFrom: blue
colorTo: purple
sdk: gradio
sdk_version: 4.44.0
app_file: app.py
pinned: false
license: mit
```

### Environment Requirements
```bash
# Minimum System Requirements
CPU: 1 core
RAM: 2GB
Storage: 1GB
Python: 3.8+

# Recommended System Requirements  
CPU: 2 cores
RAM: 4GB
Storage: 2GB
Python: 3.10+
```

### Environment Variables
```bash
HUGGINGFACE_API_TOKEN=your_token_here  # Required for AI features
DEBUG=False                            # Production setting
CACHE_DIR=data/cache                   # Cache directory
```

## 📈 Performance Metrics

### Response Time Targets
- **Simple Queries**: <2 seconds (price lookup, basic info)
- **Analysis Queries**: <15 seconds (KOL influence analysis)
- **Complex Queries**: <30 seconds (multi-coin comparison)

### Accuracy Metrics
- **Intent Recognition**: >95% accuracy for supported query types
- **Data Freshness**: Real-time price data, <5 minute social media data
- **Analysis Reliability**: Consistent scoring methodology across queries

### Availability Targets
- **Uptime**: 99%+ availability on Hugging Face Spaces
- **Error Rate**: <1% for valid queries
- **Recovery Time**: <5 minutes for service restoration

## 🔧 Development and Maintenance

### Code Quality Standards
- **Type Hints**: Comprehensive type annotations
- **Documentation**: Docstrings for all public methods
- **Testing**: Unit tests for core functionality
- **Linting**: PEP 8 compliance with automated checking

### Monitoring and Logging
- **Error Tracking**: Comprehensive exception logging
- **Performance Monitoring**: Response time tracking
- **Usage Analytics**: Query pattern analysis
- **Health Checks**: Automated system status monitoring

### Update and Maintenance
- **Dependency Management**: Regular security updates
- **Feature Rollouts**: Gradual deployment of new capabilities
- **Data Source Monitoring**: API status and reliability tracking
- **User Feedback Integration**: Continuous improvement based on usage patterns

---

**Technical Specifications v1.0** - ShillRank Agent  
*Built for Agent Demo Marathon 2024*
