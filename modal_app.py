"""
ShillRank Agent - Modal 部署版本
"""

import modal

# 创建Modal应用
app = modal.App("shillrank-agent")

# 定义镜像，包含所有依赖
image = modal.Image.debian_slim().pip_install([
    "gradio==4.44.0",
    "pandas==2.1.4",
    "numpy==1.24.3",
    "requests==2.31.0",
    "plotly==5.17.0",
    "pycoingecko==3.1.0",
    "snscrape==0.7.0.20230622",
    "python-dateutil==2.8.2",
    "python-dotenv==1.0.0",
    "diskcache==5.6.3",
    "transformers==4.36.2",
    "huggingface-hub==0.19.4"
])

# 挂载代码目录
mount = modal.Mount.from_local_dir(".", remote_path="/app")

@app.function(
    image=image,
    mounts=[mount],
    secrets=[modal.Secret.from_name("huggingface-secret")],  # 需要在Modal中设置HF Token
    ports=[7860],
    timeout=3600,  # 1小时超时
    cpu=2,  # 2个CPU核心
    memory=4096,  # 4GB内存
)
def run_agent():
    """运行ShillRank Agent"""
    import subprocess
    import os
    
    # 设置工作目录
    os.chdir("/app")
    
    # 启动应用
    subprocess.run([
        "python", "simple_agent_app.py"
    ], check=True)

@app.local_entrypoint()
def main():
    """本地入口点"""
    print("🚀 启动 ShillRank Agent on Modal...")
    run_agent.remote()

if __name__ == "__main__":
    main()
