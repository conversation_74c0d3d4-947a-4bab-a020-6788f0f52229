"""
ShillRank Agent - 最小化版本
使用最基础的Gradio Interface
"""

import gradio as gr

def shillrank_agent(message):
    """最简单的Agent函数"""
    if not message or not message.strip():
        return "Please enter a message to get started!"
    
    message = message.lower().strip()
    
    # 简单的关键词匹配
    if any(word in message for word in ["hello", "hi", "start", "what can you do"]):
        return """🤖 **Welcome to ShillRank Agent!**

I'm an AI assistant specialized in cryptocurrency KOL influence analysis.

**What I can help with:**
• Analyze KOL influence on cryptocurrencies  
• Provide market insights
• Generate influence rankings

**Try asking:**
• "Analyze PEPE coin"
• "BTC analysis" 
• "Help with crypto"

This is a demo for the Agent Demo Marathon showcasing conversational AI capabilities."""

    elif "pepe" in message:
        return """📊 **PEPE Coin KOL Analysis**

🔍 **Analysis Results:**
• Top KOL: @CryptoWhale (Influence Score: 87.3)
• Recent Mentions: 12 tweets in 7 days
• Price Impact Correlation: +2.3%
• Sentiment Score: 0.73 (Bullish)

📈 **Key Insights:**
• Strong social media presence detected
• High engagement rates from followers
• Positive sentiment trend in recent posts

⚠️ This is demo data showcasing Agent analysis capabilities."""

    elif any(word in message for word in ["btc", "bitcoin"]):
        return """💰 **Bitcoin (BTC) KOL Analysis**

📈 **Market Overview:**
• Current Price: $43,250 (+2.3%)
• Market Cap: $847B
• 24h Trading Volume: $28.5B

🔍 **Top KOL Influence:**
• Leading Influencer: @BitcoinGuru
• Influence Score: 92.5
• Recent Impact: ****% price correlation
• Follower Engagement: 15.2K avg interactions

🤖 **Agent Analysis:** Strong institutional interest with positive KOL sentiment driving momentum."""

    elif any(word in message for word in ["help", "function", "capability"]):
        return """🆘 **ShillRank Agent - Help Guide**

**Core Agent Functions:**
1. 🔍 **KOL Influence Analysis** - Analyze crypto opinion leaders
2. 💹 **Market Data Queries** - Get real-time price information  
3. 📊 **Trend Analysis** - Understand market movements
4. 📈 **Influence Rankings** - Compare KOL performance

**How to Use:**
• Ask questions in natural language
• Mention specific cryptocurrencies by name
• Request analysis or market data

**Example Queries:**
• "Analyze DOGE influence"
• "What's happening with ETH?"
• "Show me crypto market trends"

🚀 **Agent Demo Features:**
• Natural language understanding
• Intent recognition and context processing
• Intelligent response generation
• Real-world task execution simulation

This demonstrates the power of conversational AI agents!"""

    elif any(word in message for word in ["doge", "dogecoin"]):
        return """🐕 **Dogecoin (DOGE) KOL Analysis**

📊 **Influence Metrics:**
• Top KOL: @DogeWhale (Score: 84.2)
• Community Mentions: 25 posts/day
• Sentiment: Very Positive (0.81)
• Price Correlation: ****%

🚀 **Recent Activity:**
• Elon Musk mention impact: +12%
• Community engagement: High
• Meme virality factor: Strong

🤖 **Agent Insight:** DOGE shows strong community-driven influence patterns with high social media correlation."""

    elif any(word in message for word in ["eth", "ethereum"]):
        return """⚡ **Ethereum (ETH) KOL Analysis**

📈 **Technical Analysis:**
• Price: $2,580 (****%)
• Market Cap: $310B
• DeFi TVL Impact: High

🔍 **KOL Landscape:**
• Top Influencer: @EthereumGuru
• Developer Sentiment: Bullish
• Institutional Interest: Growing
• Upgrade Hype: Moderate

🤖 **Agent Analysis:** ETH shows strong technical fundamentals with positive developer and institutional KOL sentiment."""

    else:
        return f"""🤔 **Processing your query:** "{message}"

As a cryptocurrency KOL influence analysis specialist, I can help you with:

🔍 **Available Analysis:**
• "Analyze [COIN] influence" - Get detailed KOL reports
• "Help" - See all available functions  
• "Hello" - Get started with the agent

💡 **Supported Cryptocurrencies:**
• Bitcoin (BTC) • Ethereum (ETH) • Dogecoin (DOGE) • PEPE

🤖 **Agent Capabilities:**
• Natural language understanding
• Intent recognition and parameter extraction
• Contextual response generation
• Intelligent task routing and execution

Ready to analyze crypto KOL influence? Try asking about a specific cryptocurrency!"""

# 创建最简单的Interface
demo = gr.Interface(
    fn=shillrank_agent,
    inputs=gr.Textbox(
        label="Ask ShillRank Agent", 
        placeholder="e.g., Hello, what can you do?",
        lines=2
    ),
    outputs=gr.Textbox(
        label="Agent Response",
        lines=10
    ),
    title="🤖 ShillRank Agent",
    description="**Cryptocurrency KOL Influence Analysis Assistant**\n\n*Agent Demo Marathon - Track 3 Submission*\n\nConversational AI for analyzing crypto opinion leader influence and market impact.",
    examples=[
        "Hello, what can you do?",
        "Analyze PEPE coin influence",
        "BTC market analysis",
        "Help me understand your functions",
        "Analyze DOGE influence"
    ],
    theme=gr.themes.Soft(),
    allow_flagging="never"
)

if __name__ == "__main__":
    demo.launch()
