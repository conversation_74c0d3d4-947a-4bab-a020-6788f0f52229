"""
ShillRank Agent - 超简化版本
"""

import gradio as gr

def chat_fn(message, history):
    """简化的聊天函数"""
    if not message.strip():
        return history, ""
    
    # 简单响应逻辑
    if "hello" in message.lower() or "hi" in message.lower() or "what can you do" in message.lower():
        response = """🤖 **Welcome to ShillRank Agent!**

I'm an AI assistant for cryptocurrency KOL influence analysis.

**What I can help with:**
• Analyze KOL influence on cryptocurrencies
• Provide market trend insights
• Generate influence rankings

**Try asking:**
• "Analyze PEPE coin"
• "BTC price info" 
• "Help with crypto analysis"

This is a demo showcasing Agent capabilities for the Agent Demo Marathon."""
    
    elif "pepe" in message.lower():
        response = """📊 **PEPE Coin KOL Analysis**

🔍 **Analysis Results:**
• Top KOL: @CryptoWhale (Score: 87.3)
• Mentions: 12 tweets in 7 days
• Price Impact: +2.3% correlation
• Sentiment: Bullish (0.73/1.0)

📈 **Key Insights:**
• Strong social media presence
• High engagement rates
• Positive sentiment trend

⚠️ Demo data for Agent showcase purposes."""
    
    elif "btc" in message.lower() or "bitcoin" in message.lower():
        response = """💰 **Bitcoin (BTC) Analysis**

📈 **Market Data:**
• Price: $43,250 (+2.3%)
• Market Cap: $847B
• 24h Volume: $28.5B

🔍 **KOL Influence:**
• Top Influencer: @BitcoinGuru
• Influence Score: 92.5
• Recent Impact: +1.8%

🤖 **Agent Analysis:** Strong institutional interest with positive KOL sentiment."""
    
    elif "help" in message.lower():
        response = """🆘 **ShillRank Agent Help**

**Core Functions:**
1. 🔍 KOL Influence Analysis
2. 💹 Price Data Queries  
3. 📊 Market Trend Analysis
4. 📈 Influence Rankings

**How to Use:**
• Ask in natural language
• Mention specific cryptocurrencies
• Request analysis or data

**Example Queries:**
• "Analyze DOGE influence"
• "What's ETH doing?"
• "Show me crypto trends"

🚀 This demonstrates conversational AI agent capabilities!"""
    
    else:
        response = f"""🤔 **I understand you're asking about:** "{message}"

As a crypto KOL analysis specialist, I can help with:

🔍 **Try these queries:**
• "Hello" - Get started
• "Analyze [coin] influence" - KOL analysis
• "Help" - See all functions

🤖 **Agent Demo Features:**
• Natural language understanding
• Intent recognition
• Contextual responses
• Task execution

Ready to analyze crypto KOL influence? Ask me anything!"""
    
    # 更新历史
    history.append([message, response])
    return history, ""

# 创建界面
with gr.Blocks(title="ShillRank Agent") as demo:
    gr.HTML("""
    <div style="text-align: center; margin-bottom: 1rem;">
        <h1>🤖 ShillRank Agent</h1>
        <p><strong>Cryptocurrency KOL Influence Analysis Assistant</strong></p>
        <p><em>Agent Demo Marathon - Track 3 Submission</em></p>
    </div>
    """)
    
    chatbot = gr.Chatbot(label="💬 Chat with ShillRank Agent", height=400)
    msg = gr.Textbox(label="Your Message", placeholder="e.g., Hello, what can you do?")
    
    with gr.Row():
        send_btn = gr.Button("Send", variant="primary")
        clear_btn = gr.Button("Clear", variant="secondary")
    
    gr.Examples(
        examples=[
            "Hello, what can you do?",
            "Analyze PEPE coin influence",
            "BTC price and analysis",
            "Help me understand your functions"
        ],
        inputs=msg
    )
    
    gr.HTML("""
    <div style="text-align: center; margin-top: 1rem; padding: 1rem; border-top: 1px solid #eee;">
        <p>🤖 <strong>ShillRank Agent</strong> - Agent Demo Marathon Track 3</p>
        <p>🚀 Conversational AI for Crypto KOL Analysis</p>
    </div>
    """)
    
    # 绑定事件
    send_btn.click(chat_fn, [msg, chatbot], [chatbot, msg])
    msg.submit(chat_fn, [msg, chatbot], [chatbot, msg])
    clear_btn.click(lambda: ([], ""), outputs=[chatbot, msg])

if __name__ == "__main__":
    demo.launch()
