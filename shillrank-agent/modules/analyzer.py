"""
核心分析模块 - 计算 KOL 影响力评分
"""
import json
import pandas as pd
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from .kol_scraper import KOLScraper
from .price_tracker import PriceTracker
from .llm_analyzer import LLMAnalyzer
from config import SCORE_WEIGHTS, KOL_LIST_FILE

class ShillRankAnalyzer:
    def __init__(self):
        self.scraper = KOLScraper()
        self.price_tracker = PriceTracker()
        self.llm_analyzer = LLMAnalyzer()
        self.kol_list = self._load_kol_list()
        
    def _load_kol_list(self) -> List[Dict]:
        """加载 KOL 列表"""
        try:
            with open(KOL_LIST_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('kols', [])
        except Exception as e:
            print(f"加载 KOL 列表失败: {e}")
            return []
    
    def analyze_coin_influence(self, coin_query: str, days: int = 7) -> Dict:
        """分析币种的 KOL 影响力"""
        print(f"🔍 开始分析 {coin_query} 的 KOL 影响力...")
        
        # 1. 搜索币种信息
        coin_info = self.price_tracker.search_coin(coin_query)
        if not coin_info:
            return {
                'error': f'未找到币种: {coin_query}',
                'coin_info': None,
                'kol_rankings': [],
                'summary': ''
            }
        
        print(f"✅ 找到币种: {coin_info['name']} ({coin_info['symbol']})")
        
        # 2. 分析每个 KOL
        kol_results = []
        total_kols = len(self.kol_list)
        
        for i, kol in enumerate(self.kol_list):
            print(f"📊 分析 KOL {i+1}/{total_kols}: @{kol['username']}")
            
            # 获取 KOL 的币种提及数据
            kol_data = self.scraper.analyze_kol_mentions(
                kol['username'], 
                coin_info['symbol'], 
                coin_info['name'], 
                days
            )
            
            # 添加显示名称和分类
            kol_data['display_name'] = kol.get('display_name', kol['username'])
            kol_data['category'] = kol.get('category', 'unknown')
            
            # 如果有币种提及，计算价格影响
            if kol_data['coin_mentions'] > 0:
                price_impacts = []
                
                for tweet in kol_data['coin_tweets']:
                    tweet_time = tweet['date']
                    impacts = self.price_tracker.calculate_price_impact(
                        coin_info['id'], tweet_time
                    )
                    
                    if impacts:
                        # 使用24小时影响作为主要指标
                        impact_24h = impacts.get('24h', 0)
                        price_impacts.append(impact_24h)
                        tweet['price_impact'] = impacts
                
                # 计算平均价格影响
                if price_impacts:
                    kol_data['avg_price_impact'] = sum(price_impacts) / len(price_impacts)
                    kol_data['max_price_impact'] = max(price_impacts)
                    kol_data['min_price_impact'] = min(price_impacts)
                else:
                    kol_data['avg_price_impact'] = 0
                    kol_data['max_price_impact'] = 0
                    kol_data['min_price_impact'] = 0
            else:
                kol_data['avg_price_impact'] = 0
                kol_data['max_price_impact'] = 0
                kol_data['min_price_impact'] = 0
            
            kol_results.append(kol_data)
        
        # 3. 计算综合评分
        kol_rankings = self._calculate_rankings(kol_results)
        
        # 4. 生成 AI 总结
        summary = self.llm_analyzer.generate_summary(kol_rankings, coin_info['symbol'])
        
        # 5. 获取当前币价信息
        current_price = self.price_tracker.get_current_price(coin_info['id'])
        
        return {
            'coin_info': coin_info,
            'current_price': current_price,
            'kol_rankings': kol_rankings,
            'summary': summary,
            'analysis_date': datetime.now().isoformat(),
            'days_analyzed': days
        }
    
    def _calculate_rankings(self, kol_results: List[Dict]) -> List[Dict]:
        """计算 KOL 排名评分"""
        # 过滤出有提及的 KOL
        active_kols = [kol for kol in kol_results if kol['coin_mentions'] > 0]
        
        if not active_kols:
            return []
        
        # 归一化各项指标
        max_price_impact = max(abs(kol.get('avg_price_impact', 0)) for kol in active_kols)
        max_interactions = max(kol['avg_interactions'] for kol in active_kols)
        max_mentions = max(kol['coin_mentions'] for kol in active_kols)
        max_sentiment = max(abs(kol['sentiment_score']) for kol in active_kols)
        
        for kol in active_kols:
            # 归一化评分 (0-100)
            price_score = 0
            if max_price_impact > 0:
                price_score = (abs(kol.get('avg_price_impact', 0)) / max_price_impact) * 100
            
            interaction_score = 0
            if max_interactions > 0:
                interaction_score = (kol['avg_interactions'] / max_interactions) * 100
            
            frequency_score = 0
            if max_mentions > 0:
                frequency_score = (kol['coin_mentions'] / max_mentions) * 100
            
            sentiment_score = 0
            if max_sentiment > 0:
                sentiment_score = (abs(kol['sentiment_score']) / max_sentiment) * 100
            
            # 计算加权综合评分
            total_score = (
                price_score * SCORE_WEIGHTS['price_impact'] +
                interaction_score * SCORE_WEIGHTS['interactions'] +
                frequency_score * SCORE_WEIGHTS['frequency'] +
                sentiment_score * SCORE_WEIGHTS['sentiment']
            )
            
            kol['scores'] = {
                'price_impact': round(price_score, 2),
                'interactions': round(interaction_score, 2),
                'frequency': round(frequency_score, 2),
                'sentiment': round(sentiment_score, 2),
                'total': round(total_score, 2)
            }
        
        # 按总分排序
        active_kols.sort(key=lambda x: x['scores']['total'], reverse=True)
        
        # 添加排名
        for i, kol in enumerate(active_kols):
            kol['rank'] = i + 1
        
        return active_kols
    
    def get_kol_detail(self, username: str, coin_query: str, days: int = 7) -> Optional[Dict]:
        """获取单个 KOL 的详细分析"""
        coin_info = self.price_tracker.search_coin(coin_query)
        if not coin_info:
            return None
        
        kol_data = self.scraper.analyze_kol_mentions(
            username, coin_info['symbol'], coin_info['name'], days
        )
        
        # 添加价格影响分析
        if kol_data['coin_mentions'] > 0:
            for tweet in kol_data['coin_tweets']:
                tweet_time = tweet['date']
                impacts = self.price_tracker.calculate_price_impact(
                    coin_info['id'], tweet_time
                )
                tweet['price_impact'] = impacts
                
                # 添加情绪分析
                sentiment = self.llm_analyzer.analyze_sentiment(tweet['content'])
                tweet['ai_sentiment'] = sentiment
        
        return kol_data
