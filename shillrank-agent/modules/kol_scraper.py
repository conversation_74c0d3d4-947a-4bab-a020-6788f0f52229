"""
KOL 推文抓取模块 - 使用 snscrape
"""
try:
    import snscrape.modules.twitter as sntwitter
    SNSCRAPE_AVAILABLE = True
except ImportError:
    SNSCRAPE_AVAILABLE = False
    print("⚠️ snscrape 不可用，将使用演示数据")

import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import re
import diskcache as dc
import os
import random
from config import CACHE_DIR, MAX_TWEETS_PER_KOL

class KOLScraper:
    def __init__(self):
        self.cache = dc.Cache(os.path.join(CACHE_DIR, 'tweet_cache'))
        
    def scrape_user_tweets(self, username: str, days: int = 7) -> List[Dict]:
        """抓取用户指定天数内的推文"""
        cache_key = f"tweets_{username}_{days}d"

        # 检查缓存（缓存1小时）
        if cache_key in self.cache:
            cached_data = self.cache[cache_key]
            if (datetime.now() - cached_data['timestamp']).seconds < 3600:
                return cached_data['tweets']

        tweets = []
        since_date = datetime.now() - timedelta(days=days)

        try:
            # 构建搜索查询
            query = f"from:{username} since:{since_date.strftime('%Y-%m-%d')}"

            # 使用 snscrape 抓取推文
            tweet_count = 0
            for tweet in sntwitter.TwitterSearchScraper(query).get_items():
                if tweet_count >= MAX_TWEETS_PER_KOL:
                    break

                tweet_data = {
                    'id': tweet.id,
                    'content': tweet.content,
                    'date': tweet.date,
                    'username': tweet.user.username,
                    'display_name': tweet.user.displayname,
                    'like_count': tweet.likeCount or 0,
                    'retweet_count': tweet.retweetCount or 0,
                    'reply_count': tweet.replyCount or 0,
                    'quote_count': tweet.quoteCount or 0,
                    'url': tweet.url
                }

                # 计算总互动数
                tweet_data['total_interactions'] = (
                    tweet_data['like_count'] +
                    tweet_data['retweet_count'] +
                    tweet_data['reply_count'] +
                    tweet_data['quote_count']
                )

                tweets.append(tweet_data)
                tweet_count += 1

            # 缓存结果
            cache_data = {
                'tweets': tweets,
                'timestamp': datetime.now()
            }
            self.cache[cache_key] = cache_data

            print(f"抓取到 {username} 的 {len(tweets)} 条推文")
            return tweets

        except Exception as e:
            print(f"抓取 {username} 推文失败: {e}")
            # 返回演示数据
            return self._get_demo_tweets(username, days)

    def _get_demo_tweets(self, username: str, days: int) -> List[Dict]:
        """生成演示推文数据"""
        demo_tweets_templates = {
            'elonmusk': [
                "Just bought more $DOGE! 🚀 To the moon!",
                "Dogecoin is the people's crypto 🐕",
                "$PEPE looking interesting... 🐸",
                "Crypto is the future of money 💰"
            ],
            'VitalikButerin': [
                "Ethereum scaling solutions are improving rapidly",
                "Excited about the future of $ETH and DeFi",
                "Layer 2 solutions showing great promise",
                "Blockchain technology will change everything"
            ],
            'cz_binance': [
                "Building the future of finance 🏗️",
                "$BTC adoption is accelerating globally",
                "Binance continues to innovate in crypto space",
                "HODL and build! 💪"
            ],
            'saylor': [
                "$BTC is digital gold 🥇",
                "Bitcoin is the apex property of the human race",
                "MicroStrategy continues to accumulate $BTC",
                "Digital assets are the future"
            ],
            'APompliano': [
                "Bitcoin fixes this 🔧",
                "$BTC to $100k is inevitable",
                "Crypto adoption is unstoppable",
                "The future is decentralized"
            ]
        }

        tweets = []
        templates = demo_tweets_templates.get(username, [
            f"Crypto market looking bullish! 📈",
            f"Building in the crypto space 🔨",
            f"The future is digital 💻",
            f"Blockchain technology is revolutionary 🌟"
        ])

        # 生成随机推文
        for i in range(min(5, len(templates))):  # 最多5条演示推文
            tweet_time = datetime.now() - timedelta(
                days=random.randint(0, days),
                hours=random.randint(0, 23),
                minutes=random.randint(0, 59)
            )

            tweet_data = {
                'id': f"demo_{username}_{i}",
                'content': templates[i],
                'date': tweet_time,
                'username': username,
                'display_name': username.replace('_', ' ').title(),
                'like_count': random.randint(100, 50000),
                'retweet_count': random.randint(50, 10000),
                'reply_count': random.randint(10, 5000),
                'quote_count': random.randint(5, 1000),
                'url': f"https://twitter.com/{username}/status/demo_{i}"
            }

            # 计算总互动数
            tweet_data['total_interactions'] = (
                tweet_data['like_count'] +
                tweet_data['retweet_count'] +
                tweet_data['reply_count'] +
                tweet_data['quote_count']
            )

            tweets.append(tweet_data)

        print(f"生成了 {username} 的 {len(tweets)} 条演示推文")
        return tweets
    
    def filter_coin_mentions(self, tweets: List[Dict], coin_symbol: str, 
                           coin_name: str = "") -> List[Dict]:
        """筛选提到特定币种的推文"""
        coin_tweets = []
        
        # 构建搜索模式
        patterns = [
            rf'\${coin_symbol.upper()}',  # $PEPE
            rf'\${coin_symbol.lower()}',  # $pepe
            rf'\b{coin_symbol.upper()}\b',  # PEPE
            rf'\b{coin_symbol.lower()}\b',  # pepe
        ]
        
        if coin_name:
            patterns.extend([
                rf'\b{coin_name.lower()}\b',
                rf'\b{coin_name.title()}\b'
            ])
        
        for tweet in tweets:
            # 检查是否包含币种关键词
            for pattern in patterns:
                if re.search(pattern, tweet['content'], re.IGNORECASE):
                    tweet['matched_pattern'] = pattern
                    coin_tweets.append(tweet)
                    break
                    
        return coin_tweets
    
    def extract_sentiment_keywords(self, content: str) -> Dict[str, int]:
        """提取情绪关键词（简单版本）"""
        positive_words = [
            'bullish', 'moon', 'pump', 'buy', 'hodl', 'diamond', 'hands',
            'rocket', 'lambo', 'ath', 'breakout', 'surge', 'rally'
        ]
        
        negative_words = [
            'bearish', 'dump', 'sell', 'crash', 'dip', 'rekt', 'fud',
            'panic', 'drop', 'fall', 'correction', 'bubble'
        ]
        
        content_lower = content.lower()
        
        positive_count = sum(1 for word in positive_words if word in content_lower)
        negative_count = sum(1 for word in negative_words if word in content_lower)
        
        return {
            'positive': positive_count,
            'negative': negative_count,
            'sentiment_score': positive_count - negative_count
        }
    
    def analyze_kol_mentions(self, username: str, coin_symbol: str, 
                           coin_name: str = "", days: int = 7) -> Dict:
        """分析单个 KOL 对特定币种的提及情况"""
        # 获取推文
        tweets = self.scrape_user_tweets(username, days)
        
        # 筛选币种相关推文
        coin_tweets = self.filter_coin_mentions(tweets, coin_symbol, coin_name)
        
        if not coin_tweets:
            return {
                'username': username,
                'total_tweets': len(tweets),
                'coin_mentions': 0,
                'coin_tweets': [],
                'avg_interactions': 0,
                'total_interactions': 0,
                'sentiment_score': 0
            }
        
        # 计算统计数据
        total_interactions = sum(tweet['total_interactions'] for tweet in coin_tweets)
        avg_interactions = total_interactions / len(coin_tweets) if coin_tweets else 0
        
        # 分析情绪
        sentiment_scores = []
        for tweet in coin_tweets:
            sentiment = self.extract_sentiment_keywords(tweet['content'])
            tweet['sentiment'] = sentiment
            sentiment_scores.append(sentiment['sentiment_score'])
        
        avg_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0
        
        return {
            'username': username,
            'total_tweets': len(tweets),
            'coin_mentions': len(coin_tweets),
            'coin_tweets': coin_tweets,
            'avg_interactions': round(avg_interactions, 2),
            'total_interactions': total_interactions,
            'sentiment_score': round(avg_sentiment, 2)
        }
