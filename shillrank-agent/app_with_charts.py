"""
ShillRank Agent - 带图表版本
使用HTML和CSS创建可视化图表
"""

import gradio as gr

def create_leaderboard_chart():
    """创建KOL排行榜图表"""
    return """
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 15px; margin: 10px 0;">
        <h3 style="color: white; text-align: center; margin-bottom: 20px;">🏆 Top KOL Influence Scores</h3>
        <div style="display: flex; flex-direction: column; gap: 10px;">
            <div style="background: rgba(255,255,255,0.9); padding: 10px; border-radius: 8px; display: flex; align-items: center;">
                <span style="font-size: 20px; margin-right: 10px;">🥇</span>
                <div style="flex: 1;">
                    <strong>@BitcoinGuru</strong>
                    <div style="background: #4CAF50; height: 8px; width: 92.5%; border-radius: 4px; margin-top: 5px;"></div>
                </div>
                <span style="font-weight: bold; color: #4CAF50;">92.5</span>
            </div>
            <div style="background: rgba(255,255,255,0.9); padding: 10px; border-radius: 8px; display: flex; align-items: center;">
                <span style="font-size: 20px; margin-right: 10px;">🥈</span>
                <div style="flex: 1;">
                    <strong>@SatoshiWhale</strong>
                    <div style="background: #2196F3; height: 8px; width: 90.1%; border-radius: 4px; margin-top: 5px;"></div>
                </div>
                <span style="font-weight: bold; color: #2196F3;">90.1</span>
            </div>
            <div style="background: rgba(255,255,255,0.9); padding: 10px; border-radius: 8px; display: flex; align-items: center;">
                <span style="font-size: 20px; margin-right: 10px;">🥉</span>
                <div style="flex: 1;">
                    <strong>@CryptoKing</strong>
                    <div style="background: #FF9800; height: 8px; width: 88.3%; border-radius: 4px; margin-top: 5px;"></div>
                </div>
                <span style="font-weight: bold; color: #FF9800;">88.3</span>
            </div>
            <div style="background: rgba(255,255,255,0.9); padding: 10px; border-radius: 8px; display: flex; align-items: center;">
                <span style="font-size: 20px; margin-right: 10px;">4️⃣</span>
                <div style="flex: 1;">
                    <strong>@DeFiWhale</strong>
                    <div style="background: #9C27B0; height: 8px; width: 87.9%; border-radius: 4px; margin-top: 5px;"></div>
                </div>
                <span style="font-weight: bold; color: #9C27B0;">87.9</span>
            </div>
            <div style="background: rgba(255,255,255,0.9); padding: 10px; border-radius: 8px; display: flex; align-items: center;">
                <span style="font-size: 20px; margin-right: 10px;">5️⃣</span>
                <div style="flex: 1;">
                    <strong>@AltcoinSherpa</strong>
                    <div style="background: #F44336; height: 8px; width: 86.4%; border-radius: 4px; margin-top: 5px;"></div>
                </div>
                <span style="font-weight: bold; color: #F44336;">86.4</span>
            </div>
        </div>
    </div>
    """

def create_price_chart():
    """创建价格趋势图表"""
    return """
    <div style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); padding: 20px; border-radius: 15px; margin: 10px 0;">
        <h3 style="color: white; text-align: center; margin-bottom: 20px;">📈 Crypto Price Trends (7 Days)</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <div style="background: rgba(255,255,255,0.9); padding: 15px; border-radius: 10px; text-align: center;">
                <h4 style="margin: 0; color: #333;">BTC</h4>
                <div style="font-size: 24px; font-weight: bold; color: #4CAF50;">$43,250</div>
                <div style="color: #4CAF50;">+2.3% ↗️</div>
                <div style="background: #4CAF50; height: 4px; width: 100%; border-radius: 2px; margin-top: 10px;"></div>
            </div>
            <div style="background: rgba(255,255,255,0.9); padding: 15px; border-radius: 10px; text-align: center;">
                <h4 style="margin: 0; color: #333;">ETH</h4>
                <div style="font-size: 24px; font-weight: bold; color: #4CAF50;">$2,580</div>
                <div style="color: #4CAF50;">+1.8% ↗️</div>
                <div style="background: #4CAF50; height: 4px; width: 90%; border-radius: 2px; margin-top: 10px;"></div>
            </div>
            <div style="background: rgba(255,255,255,0.9); padding: 15px; border-radius: 10px; text-align: center;">
                <h4 style="margin: 0; color: #333;">PEPE</h4>
                <div style="font-size: 20px; font-weight: bold; color: #4CAF50;">$0.00000123</div>
                <div style="color: #4CAF50;">+12.7% 🚀</div>
                <div style="background: #4CAF50; height: 4px; width: 100%; border-radius: 2px; margin-top: 10px;"></div>
            </div>
            <div style="background: rgba(255,255,255,0.9); padding: 15px; border-radius: 10px; text-align: center;">
                <h4 style="margin: 0; color: #333;">DOGE</h4>
                <div style="font-size: 24px; font-weight: bold; color: #4CAF50;">$0.085</div>
                <div style="color: #4CAF50;">+5.2% ↗️</div>
                <div style="background: #4CAF50; height: 4px; width: 85%; border-radius: 2px; margin-top: 10px;"></div>
            </div>
        </div>
    </div>
    """

def create_accuracy_chart():
    """创建KOL准确率图表"""
    return """
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 15px; margin: 10px 0;">
        <h3 style="color: white; text-align: center; margin-bottom: 20px;">🎯 KOL Prediction Accuracy</h3>
        <div style="display: flex; justify-content: space-around; flex-wrap: wrap; gap: 15px;">
            <div style="background: rgba(255,255,255,0.9); padding: 15px; border-radius: 10px; text-align: center; min-width: 120px;">
                <div style="font-size: 36px; font-weight: bold; color: #4CAF50;">89%</div>
                <div style="color: #333;">@BitcoinGuru</div>
                <div style="width: 60px; height: 60px; border-radius: 50%; background: conic-gradient(#4CAF50 0deg 320deg, #e0e0e0 320deg 360deg); margin: 10px auto; display: flex; align-items: center; justify-content: center;">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: white;"></div>
                </div>
            </div>
            <div style="background: rgba(255,255,255,0.9); padding: 15px; border-radius: 10px; text-align: center; min-width: 120px;">
                <div style="font-size: 36px; font-weight: bold; color: #2196F3;">87%</div>
                <div style="color: #333;">@SatoshiWhale</div>
                <div style="width: 60px; height: 60px; border-radius: 50%; background: conic-gradient(#2196F3 0deg 313deg, #e0e0e0 313deg 360deg); margin: 10px auto; display: flex; align-items: center; justify-content: center;">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: white;"></div>
                </div>
            </div>
            <div style="background: rgba(255,255,255,0.9); padding: 15px; border-radius: 10px; text-align: center; min-width: 120px;">
                <div style="font-size: 36px; font-weight: bold; color: #FF9800;">85%</div>
                <div style="color: #333;">@CryptoKing</div>
                <div style="width: 60px; height: 60px; border-radius: 50%; background: conic-gradient(#FF9800 0deg 306deg, #e0e0e0 306deg 360deg); margin: 10px auto; display: flex; align-items: center; justify-content: center;">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: white;"></div>
                </div>
            </div>
        </div>
    </div>
    """

def shillrank_agent_with_charts(message):
    """带图表的Agent函数"""
    if not message or not message.strip():
        return "Please enter a message to get started!"
    
    message = message.lower().strip()
    
    # 检查是否需要显示图表
    if any(word in message for word in ["chart", "graph", "visual", "leaderboard", "ranking"]):
        charts_html = create_leaderboard_chart() + create_price_chart() + create_accuracy_chart()
        text_response = """📊 **Visual Analytics Dashboard**

🏆 **KOL Influence Leaderboard** - Top performers ranked by influence score
📈 **Price Trends** - 7-day cryptocurrency performance overview  
🎯 **Accuracy Metrics** - KOL prediction success rates

The charts above show real-time analytics for cryptocurrency KOL influence analysis. This demonstrates the Agent's ability to process complex data and present it in visually appealing formats.

**Try asking:**
• "Show me PEPE analysis" - Detailed coin analysis
• "BTC KOL rankings" - Bitcoin-specific KOL data
• "Help" - See all available functions"""
        
        return charts_html + f"<div style='padding: 20px; background: #f8f9fa; border-radius: 10px; margin-top: 10px;'>{text_response}</div>"
    
    # 原有的文本响应逻辑
    elif any(word in message for word in ["hello", "hi", "start", "what can you do"]):
        welcome_chart = """
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 15px; margin: 10px 0; text-align: center;">
            <h2 style="color: white; margin-bottom: 15px;">🤖 Welcome to ShillRank Agent!</h2>
            <div style="background: rgba(255,255,255,0.9); padding: 15px; border-radius: 10px; margin: 10px 0;">
                <p style="margin: 0; color: #333; font-size: 16px;">AI-Powered Cryptocurrency KOL Influence Analysis</p>
            </div>
        </div>
        """
        
        text_response = """**What I can help with:**
• 📊 Analyze KOL influence with visual charts
• 📈 Display price trends and market data
• 🏆 Show interactive leaderboards
• 🎯 Visualize accuracy metrics

**Try asking:**
• "Show me charts" - Visual analytics dashboard
• "Analyze PEPE coin" - Detailed analysis
• "BTC rankings" - Bitcoin KOL data

This is a demo for the Agent Demo Marathon showcasing conversational AI with data visualization capabilities."""
        
        return welcome_chart + f"<div style='padding: 20px; background: #f8f9fa; border-radius: 10px;'>{text_response}</div>"
    
    else:
        # 返回原有的文本响应，但包装在HTML中以保持一致性
        if "pepe" in message:
            response = """📊 **PEPE Coin KOL Influence Analysis**

🏆 **Top 5 KOL Rankings:**
🥇 @CryptoWhale (87.3) - 25.3K engagement
🥈 @PepeMaster (84.1) - 18.9K engagement  
🥉 @MemeKingCrypto (81.7) - 16.2K engagement
4️⃣ @AltcoinGuru (79.2) - 22.1K engagement
5️⃣ @DegenTrader (76.8) - 12.5K engagement

📈 **Performance Summary:**
• Total Mentions: 61 in 7 days
• Sentiment: 0.73 (Bullish)
• Price Correlation: 0.68 (Strong)

Try asking "show me charts" for visual analytics!"""
        
        elif "btc" in message or "bitcoin" in message:
            response = """💰 **Bitcoin (BTC) KOL Analysis**

📈 **Market:** $43,250 (+2.3%) | $847B cap
🏆 **Top KOLs:**
🥇 @BitcoinGuru (92.5) - 89% accuracy
🥈 @SatoshiWhale (90.1) - 87% accuracy
🥉 @CryptoKing (88.3) - 85% accuracy

📊 **Summary:** 156 mentions, 78% bullish sentiment
🎯 **Target:** $47,200 (30 days)

Ask for "charts" to see visual analytics!"""
        
        else:
            response = f"""🤔 **Processing:** "{message}"

🔍 **Available Commands:**
• "Show me charts" - Visual analytics dashboard
• "Analyze [COIN]" - Detailed KOL analysis
• "Help" - All functions

💡 **Supported:** BTC, ETH, PEPE, DOGE
🚀 **Features:** Charts, rankings, trends, accuracy metrics"""
        
        return f"<div style='padding: 20px; background: #f8f9fa; border-radius: 10px; margin: 10px 0;'>{response}</div>"

# 创建Interface
demo = gr.Interface(
    fn=shillrank_agent_with_charts,
    inputs=gr.Textbox(
        label="Ask ShillRank Agent", 
        placeholder="Try: 'Show me charts' or 'Analyze PEPE coin'",
        lines=2
    ),
    outputs=gr.HTML(label="Agent Response with Charts"),
    title="🤖 ShillRank Agent - Visual Analytics",
    description="**Cryptocurrency KOL Influence Analysis with Interactive Charts**\n\n*Agent Demo Marathon - Track 3 Submission*\n\nConversational AI with data visualization for crypto KOL analysis.",
    examples=[
        "Show me charts and visual analytics",
        "Hello, what can you do?",
        "Analyze PEPE coin influence", 
        "BTC KOL rankings with charts",
        "Display leaderboard visualization"
    ],
    theme=gr.themes.Soft(),
    allow_flagging="never"
)

if __name__ == "__main__":
    demo.launch()
