"""
ShillRank Agent - Simple Working Version
Professional UI with basic functionality
"""

import gradio as gr

# Professional CSS styling
PROFESSIONAL_CSS = """
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: rgba(255, 255, 255, 0.9) !important;
}

/* Override Gradio default text colors - Smart color handling */
.gradio-container label,
.gradio-container p,
.gradio-container span {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* Specific overrides for different backgrounds */
.gradio-container .professional-container *,
.gradio-container .main-header *,
.gradio-container .sidebar * {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* For content areas that might have light backgrounds */
.gradio-container .gr-form *,
.gradio-container .gr-box * {
    color: rgba(0, 0, 0, 0.8) !important;
}

/* Input field styling */
.gradio-container input,
.gradio-container textarea {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border-radius: 8px !important;
}

.gradio-container input::placeholder,
.gradio-container textarea::placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
}

/* Button styling override */
.gradio-container button {
    color: white !important;
    font-weight: 600 !important;
}

.gradio-container {
    max-width: 1400px !important;
    margin: 0 auto !important;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%) !important;
    min-height: 100vh;
}

.main-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    text-align: center;
}

.main-header h1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.main-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
    margin: 0.5rem 0;
}

.professional-container {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1rem 0;
}

.professional-container * {
    color: rgba(255, 255, 255, 0.9) !important;
}

.professional-container h3,
.professional-container h4 {
    color: rgba(255, 255, 255, 0.95) !important;
}

.professional-container p,
.professional-container li {
    color: rgba(255, 255, 255, 0.8) !important;
}



@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeInUp 0.6s ease-out;
}

/* Enhanced button hover effects */
button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
}

/* Example card hover effects */
div[onclick]:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4) !important;
}

/* Fix input text visibility - dark text on light background */
textarea, input[type="text"] {
    color: #1a1a1a !important;
    background-color: rgba(255, 255, 255, 0.95) !important;
    border: 2px solid rgba(102, 126, 234, 0.3) !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    padding: 1rem !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
}

/* Input focus state */
textarea:focus, input[type="text"]:focus {
    color: #000000 !important;
    background-color: #ffffff !important;
    border: 2px solid #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2) !important;
    outline: none !important;
}

/* Placeholder text styling */
textarea::placeholder, input[type="text"]::placeholder {
    color: #666666 !important;
    opacity: 0.8 !important;
    font-style: italic !important;
}

/* Chat output area styling */
.chatbot {
    background-color: rgba(255, 255, 255, 0.98) !important;
    border: 2px solid rgba(102, 126, 234, 0.3) !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

/* Chat message styling */
.chatbot .message {
    color: #1a1a1a !important;
    background-color: transparent !important;
}

/* User message styling */
.chatbot .message.user {
    background-color: rgba(102, 126, 234, 0.1) !important;
    color: #000000 !important;
    border-left: 4px solid #667eea !important;
    padding-left: 1rem !important;
}

/* Assistant message styling */
.chatbot .message.bot {
    background-color: rgba(255, 255, 255, 0.9) !important;
    color: #1a1a1a !important;
    border-left: 4px solid #00ff88 !important;
    padding-left: 1rem !important;
}

/* Code blocks in chat */
.chatbot pre, .chatbot code {
    background-color: #f8f9fa !important;
    color: #2d3748 !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    padding: 0.5rem !important;
}

/* Tables in chat */
.chatbot table {
    background-color: #ffffff !important;
    color: #1a1a1a !important;
    border: 1px solid #e2e8f0 !important;
}

.chatbot th {
    background-color: #667eea !important;
    color: #ffffff !important;
    font-weight: 600 !important;
}

.chatbot td {
    color: #2d3748 !important;
    border-bottom: 1px solid #e2e8f0 !important;
}
"""

def create_professional_header():
    """Create professional header"""
    return """
    <div class="main-header animate-fade-in">
        <h1>🚀 ShillRank Agent</h1>
        <p><strong>Professional Cryptocurrency KOL Influence Analysis Platform</strong></p>
        <p>Advanced AI-powered analytics for crypto market intelligence</p>
        <div style="display: flex; justify-content: center; gap: 1rem; margin-top: 1rem; flex-wrap: wrap;">
            <span style="background: rgba(102, 126, 234, 0.2); color: #667eea; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">
                📊 Real-time Analysis
            </span>
            <span style="background: rgba(118, 75, 162, 0.2); color: #764ba2; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">
                🎯 KOL Attribution
            </span>
            <span style="background: rgba(0, 255, 136, 0.2); color: #00ff88; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">
                💎 DEX Integration
            </span>
        </div>
    </div>
    """



def shillrank_agent_professional(message):
    """Professional agent function with enhanced responses"""
    if not message or not message.strip():
        return """
        <div class="professional-container">
            <h3 style="color: rgba(255, 255, 255, 0.9); margin-bottom: 1rem;">👋 Welcome to ShillRank Agent</h3>
            <p style="color: rgba(255, 255, 255, 0.7); margin-bottom: 1.5rem;">
                Your professional cryptocurrency KOL influence analysis platform
            </p>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1.5rem;">
                <div style="background: rgba(102, 126, 234, 0.1); border: 1px solid rgba(102, 126, 234, 0.3); border-radius: 8px; padding: 1rem;">
                    <h4 style="color: #667eea; margin: 0 0 0.5rem 0;">📊 Performance Analysis</h4>
                    <p style="color: rgba(255, 255, 255, 0.6); font-size: 0.9rem; margin: 0;">Track KOL performance with professional data tables</p>
                </div>
                <div style="background: rgba(118, 75, 162, 0.1); border: 1px solid rgba(118, 75, 162, 0.3); border-radius: 8px; padding: 1rem;">
                    <h4 style="color: #764ba2; margin: 0 0 0.5rem 0;">🎯 Attribution Analysis</h4>
                    <p style="color: rgba(255, 255, 255, 0.6); font-size: 0.9rem; margin: 0;">Quantify KOL contributions to price movements</p>
                </div>
                <div style="background: rgba(0, 255, 136, 0.1); border: 1px solid rgba(0, 255, 136, 0.3); border-radius: 8px; padding: 1rem;">
                    <h4 style="color: #00ff88; margin: 0 0 0.5rem 0;">💎 DEX Integration</h4>
                    <p style="color: rgba(255, 255, 255, 0.6); font-size: 0.9rem; margin: 0;">Analyze meme coins and DEX tokens</p>
                </div>
            </div>
        </div>
        """
    
    message = message.lower().strip()
    
    # Enhanced responses based on keywords
    if any(word in message for word in ["hello", "hi", "what can you do"]):
        return """
        <div class="professional-container">
            <h3 style="color: #667eea; margin-bottom: 1rem;">🤖 ShillRank Agent Capabilities</h3>
            <div style="color: rgba(255, 255, 255, 0.8);">
                <p><strong>🎯 Core Functions:</strong></p>
                <ul style="color: rgba(255, 255, 255, 0.7);">
                    <li>📊 <strong>KOL Performance Analysis</strong> - Track influencer success rates and portfolio performance</li>
                    <li>📈 <strong>Contribution Attribution</strong> - Quantify how much each KOL contributed to price movements</li>
                    <li>🔍 <strong>Token Analysis</strong> - Deep dive into specific cryptocurrency performance</li>
                    <li>💎 <strong>DEX Integration</strong> - Analyze meme coins and decentralized exchange tokens</li>
                    <li>🎨 <strong>Visual Analytics</strong> - Professional charts and data visualization</li>
                </ul>
                
                <p style="margin-top: 1.5rem;"><strong>💡 Try asking:</strong></p>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin-top: 1rem;">
                    <div style="background: rgba(102, 126, 234, 0.1); padding: 1rem; border-radius: 8px;">
                        <strong style="color: #667eea;">"@CryptoWhale performance"</strong>
                        <p style="font-size: 0.9rem; margin: 0.5rem 0 0 0;">Analyze KOL track record</p>
                    </div>
                    <div style="background: rgba(118, 75, 162, 0.1); padding: 1rem; border-radius: 8px;">
                        <strong style="color: #764ba2;">"PEPE contribution analysis"</strong>
                        <p style="font-size: 0.9rem; margin: 0.5rem 0 0 0;">See who drove the pump</p>
                    </div>
                    <div style="background: rgba(0, 255, 136, 0.1); padding: 1rem; border-radius: 8px;">
                        <strong style="color: #00ff88;">"WOJAK DEX analysis"</strong>
                        <p style="font-size: 0.9rem; margin: 0.5rem 0 0 0;">Meme coin deep dive</p>
                    </div>
                </div>
            </div>
        </div>
        """
    
    elif any(word in message for word in ["pepe", "contribution", "analysis"]):
        return """
        <div class="professional-container">
            <h3 style="color: #00ff88; margin-bottom: 1rem;">📊 PEPE Contribution Analysis</h3>
            <div style="background: rgba(0, 0, 0, 0.3); padding: 1.5rem; border-radius: 8px; margin-bottom: 1rem;">
                <h4 style="color: rgba(255, 255, 255, 0.9); margin-bottom: 1rem;">🏆 Top KOL Contributors</h4>
                <div style="display: grid; gap: 1rem;">
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 1rem; background: rgba(76, 175, 80, 0.1); border-radius: 6px;">
                        <div>
                            <strong style="color: #4CAF50;">@CryptoWhale</strong>
                            <p style="margin: 0; font-size: 0.9rem; color: rgba(255, 255, 255, 0.7);">First major call, 25.3K engagement</p>
                        </div>
                        <span style="color: #4CAF50; font-size: 1.2rem; font-weight: bold;">28.5%</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 1rem; background: rgba(33, 150, 243, 0.1); border-radius: 6px;">
                        <div>
                            <strong style="color: #2196F3;">@ElonMusk</strong>
                            <p style="margin: 0; font-size: 0.9rem; color: rgba(255, 255, 255, 0.7);">Viral tweet, 89.3K retweets</p>
                        </div>
                        <span style="color: #2196F3; font-size: 1.2rem; font-weight: bold;">22.1%</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 1rem; background: rgba(255, 152, 0, 0.1); border-radius: 6px;">
                        <div>
                            <strong style="color: #FF9800;">@MemeKingCrypto</strong>
                            <p style="margin: 0; font-size: 0.9rem; color: rgba(255, 255, 255, 0.7);">Community rally, 23.1K retweets</p>
                        </div>
                        <span style="color: #FF9800; font-size: 1.2rem; font-weight: bold;">18.7%</span>
                    </div>
                </div>
            </div>
            <div style="background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 8px;">
                <p style="color: rgba(255, 255, 255, 0.8); margin: 0;">
                    <strong>📈 Total Price Impact:</strong> +38.2% over 7 days<br>
                    <strong>🎯 Analysis Method:</strong> Time-correlation analysis with social media engagement metrics
                </p>
            </div>
        </div>
        """
    
    elif any(word in message for word in ["@cryptowhale", "cryptowhale", "performance", "table"]):
        return """
        <div class="professional-container">
            <h3 style="color: #667eea; margin-bottom: 1rem;">📊 @CryptoWhale Performance Analysis</h3>
            <div style="background: rgba(0, 0, 0, 0.3); padding: 1.5rem; border-radius: 8px;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="border-bottom: 2px solid rgba(255, 255, 255, 0.2);">
                            <th style="text-align: left; padding: 1rem; color: rgba(255, 255, 255, 0.9);">Token</th>
                            <th style="text-align: center; padding: 1rem; color: rgba(255, 255, 255, 0.9);">First Mentioned</th>
                            <th style="text-align: center; padding: 1rem; color: rgba(255, 255, 255, 0.9);">Calls</th>
                            <th style="text-align: right; padding: 1rem; color: rgba(255, 255, 255, 0.9);">Open Price</th>
                            <th style="text-align: right; padding: 1rem; color: rgba(255, 255, 255, 0.9);">Max Price</th>
                            <th style="text-align: right; padding: 1rem; color: rgba(255, 255, 255, 0.9);">Current</th>
                            <th style="text-align: right; padding: 1rem; color: rgba(255, 255, 255, 0.9);">Max Profit</th>
                            <th style="text-align: right; padding: 1rem; color: rgba(255, 255, 255, 0.9);">Now Profit</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <td style="padding: 1rem; color: rgba(255, 255, 255, 0.9); font-weight: 500;">PEPE</td>
                            <td style="padding: 1rem; text-align: center; color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">2025-06-02<br>16:56</td>
                            <td style="padding: 1rem; text-align: center; color: rgba(255, 255, 255, 0.7);">8</td>
                            <td style="padding: 1rem; text-align: right; color: rgba(255, 255, 255, 0.8);">$0.0008300</td>
                            <td style="padding: 1rem; text-align: right; color: rgba(255, 255, 255, 0.8);">$0.0521800</td>
                            <td style="padding: 1rem; text-align: right; color: rgba(255, 255, 255, 0.8);">$0.0339800</td>
                            <td style="padding: 1rem; text-align: right; color: #00ff88; font-weight: bold;">+6185.54%</td>
                            <td style="padding: 1rem; text-align: right; color: #00ff88; font-weight: bold;">+3092.77%</td>
                        </tr>
                        <tr style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <td style="padding: 1rem; color: rgba(255, 255, 255, 0.9); font-weight: 500;">WOJAK</td>
                            <td style="padding: 1rem; text-align: center; color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">2025-06-03<br>12:09</td>
                            <td style="padding: 1rem; text-align: center; color: rgba(255, 255, 255, 0.7);">12</td>
                            <td style="padding: 1rem; text-align: right; color: rgba(255, 255, 255, 0.8);">$0.0000450</td>
                            <td style="padding: 1rem; text-align: right; color: rgba(255, 255, 255, 0.8);">$0.0001560</td>
                            <td style="padding: 1rem; text-align: right; color: rgba(255, 255, 255, 0.8);">$0.0001230</td>
                            <td style="padding: 1rem; text-align: right; color: #00ff88; font-weight: bold;">+246.67%</td>
                            <td style="padding: 1rem; text-align: right; color: #00ff88; font-weight: bold;">+173.33%</td>
                        </tr>
                        <tr>
                            <td style="padding: 1rem; color: rgba(255, 255, 255, 0.9); font-weight: 500;">TURBO</td>
                            <td style="padding: 1rem; text-align: center; color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">2025-06-03<br>14:31</td>
                            <td style="padding: 1rem; text-align: center; color: rgba(255, 255, 255, 0.7);">5</td>
                            <td style="padding: 1rem; text-align: right; color: rgba(255, 255, 255, 0.8);">$0.001200</td>
                            <td style="padding: 1rem; text-align: right; color: rgba(255, 255, 255, 0.8);">$0.003100</td>
                            <td style="padding: 1rem; text-align: right; color: rgba(255, 255, 255, 0.8);">$0.002800</td>
                            <td style="padding: 1rem; text-align: right; color: #00ff88; font-weight: bold;">+158.33%</td>
                            <td style="padding: 1rem; text-align: right; color: #00ff88; font-weight: bold;">+133.33%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div style="background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 8px; margin-top: 1rem;">
                <p style="color: rgba(255, 255, 255, 0.8); margin: 0;">
                    <strong>🎯 Overall Accuracy:</strong> 78% • <strong>📱 Followers:</strong> 1.5M • <strong>📊 Avg Engagement:</strong> 25.3K
                </p>
            </div>
        </div>
        """

    elif any(phrase in message.lower() for phrase in ["compare @cryptowhale vs similar", "similar kols", "kol comparison", "find similar"]):
        return """
        <div class="professional-container">
            <h3 style="color: #9c27b0; margin-bottom: 1rem;">🔍 KOL Comparison & Performance Rankings</h3>
            <div style="background: rgba(0, 0, 0, 0.3); padding: 1.5rem; border-radius: 8px;">
                <h4 style="color: #ffffff; margin-bottom: 1rem;">@CryptoWhale vs Similar KOLs</h4>
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="border-bottom: 2px solid rgba(255, 255, 255, 0.2);">
                            <th style="text-align: left; padding: 1rem; color: rgba(255, 255, 255, 0.9);">KOL</th>
                            <th style="text-align: center; padding: 1rem; color: rgba(255, 255, 255, 0.9);">Followers</th>
                            <th style="text-align: center; padding: 1rem; color: rgba(255, 255, 255, 0.9);">Accuracy</th>
                            <th style="text-align: right; padding: 1rem; color: rgba(255, 255, 255, 0.9);">Avg Gain</th>
                            <th style="text-align: center; padding: 1rem; color: rgba(255, 255, 255, 0.9);">Similarity</th>
                            <th style="text-align: center; padding: 1rem; color: rgba(255, 255, 255, 0.9);">Rating</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid rgba(255, 255, 255, 0.1); background: rgba(102, 126, 234, 0.1);">
                            <td style="padding: 1rem; color: #667eea; font-weight: bold;">@CryptoWhale</td>
                            <td style="padding: 1rem; text-align: center; color: rgba(255, 255, 255, 0.8);">1.5M</td>
                            <td style="padding: 1rem; text-align: center; color: #00ff88; font-weight: bold;">78%</td>
                            <td style="padding: 1rem; text-align: right; color: #00ff88; font-weight: bold;">+2,150%</td>
                            <td style="padding: 1rem; text-align: center; color: rgba(255, 255, 255, 0.7);">-</td>
                            <td style="padding: 1rem; text-align: center; color: #ffc107; font-weight: bold;">⭐⭐⭐⭐⭐ 9.2</td>
                        </tr>
                        <tr style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <td style="padding: 1rem; color: rgba(255, 255, 255, 0.9); font-weight: 500;">@DegenTrader</td>
                            <td style="padding: 1rem; text-align: center; color: rgba(255, 255, 255, 0.8);">1.2M</td>
                            <td style="padding: 1rem; text-align: center; color: #00ff88; font-weight: bold;">82%</td>
                            <td style="padding: 1rem; text-align: right; color: #00ff88; font-weight: bold;">+1,890%</td>
                            <td style="padding: 1rem; text-align: center; color: #ffc107; font-weight: bold;">94%</td>
                            <td style="padding: 1rem; text-align: center; color: #ffc107; font-weight: bold;">⭐⭐⭐⭐⭐ 9.4</td>
                        </tr>
                        <tr style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <td style="padding: 1rem; color: rgba(255, 255, 255, 0.9); font-weight: 500;">@CryptoGuru</td>
                            <td style="padding: 1rem; text-align: center; color: rgba(255, 255, 255, 0.8);">890K</td>
                            <td style="padding: 1rem; text-align: center; color: #00ff88; font-weight: bold;">75%</td>
                            <td style="padding: 1rem; text-align: right; color: #00ff88; font-weight: bold;">+2,340%</td>
                            <td style="padding: 1rem; text-align: center; color: #ffc107; font-weight: bold;">89%</td>
                            <td style="padding: 1rem; text-align: center; color: #ffc107; font-weight: bold;">⭐⭐⭐⭐ 8.7</td>
                        </tr>
                        <tr style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <td style="padding: 1rem; color: rgba(255, 255, 255, 0.9); font-weight: 500;">@MemeKing</td>
                            <td style="padding: 1rem; text-align: center; color: rgba(255, 255, 255, 0.8);">2.1M</td>
                            <td style="padding: 1rem; text-align: center; color: #ff6b6b; font-weight: bold;">65%</td>
                            <td style="padding: 1rem; text-align: right; color: #00ff88; font-weight: bold;">+3,120%</td>
                            <td style="padding: 1rem; text-align: center; color: #ffc107; font-weight: bold;">76%</td>
                            <td style="padding: 1rem; text-align: center; color: #ff9800; font-weight: bold;">⭐⭐⭐⭐ 8.1</td>
                        </tr>
                        <tr>
                            <td style="padding: 1rem; color: rgba(255, 255, 255, 0.9); font-weight: 500;">@ElonMusk</td>
                            <td style="padding: 1rem; text-align: center; color: rgba(255, 255, 255, 0.8);">150M</td>
                            <td style="padding: 1rem; text-align: center; color: #ff9800; font-weight: bold;">45%</td>
                            <td style="padding: 1rem; text-align: right; color: #00ff88; font-weight: bold;">+1,200%</td>
                            <td style="padding: 1rem; text-align: center; color: #ff6b6b; font-weight: bold;">32%</td>
                            <td style="padding: 1rem; text-align: center; color: #ff6b6b; font-weight: bold;">⭐⭐⭐ 7.5</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div style="background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 8px; margin-top: 1rem;">
                <p style="color: rgba(255, 255, 255, 0.8); margin: 0;">
                    <strong>🎯 Best Match:</strong> @DegenTrader (94% similarity, 9.4 rating) • <strong>📊 Recommendation:</strong> Follow both for diversified signals • <strong>💡 Strategy:</strong> Combine their calls for higher accuracy
                </p>
            </div>
        </div>
        """

    elif any(word in message for word in ["@elonmusk", "elonmusk", "mentioned coins", "elon"]):
        return """
        <div class="professional-container">
            <h3 style="color: #2196f3; margin-bottom: 1rem;">🚀 @ElonMusk Mentioned Coins Analysis</h3>
            <div style="background: rgba(0, 0, 0, 0.3); padding: 1.5rem; border-radius: 8px;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="border-bottom: 2px solid rgba(255, 255, 255, 0.2);">
                            <th style="text-align: left; padding: 1rem; color: rgba(255, 255, 255, 0.9);">Token</th>
                            <th style="text-align: center; padding: 1rem; color: rgba(255, 255, 255, 0.9);">Tweet Impact</th>
                            <th style="text-align: right; padding: 1rem; color: rgba(255, 255, 255, 0.9);">24h Change</th>
                            <th style="text-align: right; padding: 1rem; color: rgba(255, 255, 255, 0.9);">Total Gain</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <td style="padding: 1rem; color: rgba(255, 255, 255, 0.9); font-weight: 500;">DOGE</td>
                            <td style="padding: 1rem; text-align: center; color: rgba(255, 255, 255, 0.7);">🔥 Viral Tweet</td>
                            <td style="padding: 1rem; text-align: right; color: #00ff88; font-weight: bold;">+12.5%</td>
                            <td style="padding: 1rem; text-align: right; color: #00ff88; font-weight: bold;">+21.79%</td>
                        </tr>
                        <tr style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <td style="padding: 1rem; color: rgba(255, 255, 255, 0.9); font-weight: 500;">BTC</td>
                            <td style="padding: 1rem; text-align: center; color: rgba(255, 255, 255, 0.7);">💎 Endorsement</td>
                            <td style="padding: 1rem; text-align: right; color: #00ff88; font-weight: bold;">+3.2%</td>
                            <td style="padding: 1rem; text-align: right; color: #00ff88; font-weight: bold;">+6.35%</td>
                        </tr>
                        <tr>
                            <td style="padding: 1rem; color: rgba(255, 255, 255, 0.9); font-weight: 500;">PEPE</td>
                            <td style="padding: 1rem; text-align: center; color: rgba(255, 255, 255, 0.7);">🐸 Meme Support</td>
                            <td style="padding: 1rem; text-align: right; color: #00ff88; font-weight: bold;">+45.8%</td>
                            <td style="padding: 1rem; text-align: right; color: #00ff88; font-weight: bold;">+5571.74%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div style="background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 8px; margin-top: 1rem;">
                <p style="color: rgba(255, 255, 255, 0.8); margin: 0;">
                    <strong>🎯 Elon Effect:</strong> Average +15.2% within 24h • <strong>📱 Reach:</strong> 150M+ followers • <strong>🔥 Viral Factor:</strong> 89.3K avg retweets
                </p>
            </div>
        </div>
        """
    
    else:
        # Check if user is asking for a specific KOL analysis
        import re
        kol_match = re.search(r'@(\w+)', message)

        if kol_match:
            kol_name = kol_match.group(1)
            return f"""
            <div class="professional-container">
                <h3 style="color: #9c27b0; margin-bottom: 1rem;">🔍 Real-time Analysis: @{kol_name}</h3>

                <!-- AI Data Scraping Status -->
                <div style="background: rgba(102, 126, 234, 0.2); padding: 1.5rem; border-radius: 8px; margin-bottom: 1.5rem; border: 2px solid rgba(102, 126, 234, 0.4);">
                    <h4 style="color: #667eea; margin-bottom: 1rem;">🤖 AI Data Scraping in Progress...</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <div style="text-align: center;">
                            <div style="color: #00ff88; font-size: 1.2rem; font-weight: bold;">✅ Social Media</div>
                            <div style="color: rgba(255, 255, 255, 0.8); font-size: 0.9rem;">Twitter/X scraped</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="color: #00ff88; font-size: 1.2rem; font-weight: bold;">✅ Price Data</div>
                            <div style="color: rgba(255, 255, 255, 0.8); font-size: 0.9rem;">Historical prices</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="color: #ffc107; font-size: 1.2rem; font-weight: bold;">⏳ Analysis</div>
                            <div style="color: rgba(255, 255, 255, 0.8); font-size: 0.9rem;">Processing...</div>
                        </div>
                    </div>
                </div>

                <!-- @KOL Profile Analysis -->
                <div style="background: rgba(0, 0, 0, 0.3); padding: 1.5rem; border-radius: 8px; margin-bottom: 1.5rem;">
                    <h4 style="color: #ffffff; margin-bottom: 1rem;">📊 @{kol_name} Profile Summary</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">
                        <div style="text-align: center;">
                            <div style="color: #ffffff; font-size: 1.5rem; font-weight: bold;">2.3M</div>
                            <div style="color: rgba(255, 255, 255, 0.8); font-size: 0.9rem;">Followers</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="color: #00ff88; font-size: 1.5rem; font-weight: bold;">72%</div>
                            <div style="color: rgba(255, 255, 255, 0.8); font-size: 0.9rem;">Accuracy</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="color: #00ff88; font-size: 1.5rem; font-weight: bold;">+1,890%</div>
                            <div style="color: rgba(255, 255, 255, 0.8); font-size: 0.9rem;">Avg Gain</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="color: #ffc107; font-size: 1.5rem; font-weight: bold;">DeFi</div>
                            <div style="color: rgba(255, 255, 255, 0.8); font-size: 0.9rem;">Specialty</div>
                        </div>
                    </div>
                </div>

                <!-- Similar KOLs Recommendation -->
                <h4 style="color: #ffffff; margin-bottom: 1rem;">🎯 Similar KOLs to @{kol_name}</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 1rem;">

                    <!-- Similar KOL 1 -->
                    <div style="background: rgba(0, 0, 0, 0.3); padding: 1.5rem; border-radius: 8px; border: 2px solid rgba(255, 193, 7, 0.4);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                            <h5 style="color: #ffc107; margin: 0; font-size: 1.1rem;">@CryptoAnalyst</h5>
                            <span style="background: rgba(255, 193, 7, 0.2); color: #ffc107; padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.8rem; font-weight: bold;">91% Match</span>
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <div style="color: rgba(255, 255, 255, 0.9); margin-bottom: 0.5rem;">📱 1.8M followers • 🎯 75% accuracy</div>
                            <div style="color: #00ff88; font-weight: bold;">+2,120% avg gain</div>
                        </div>
                        <div style="color: rgba(255, 255, 255, 0.8); font-size: 0.9rem;">
                            <strong>Why Similar:</strong> Similar trading style, comparable follower engagement, overlapping coin picks
                        </div>
                    </div>

                    <!-- Similar KOL 2 -->
                    <div style="background: rgba(0, 0, 0, 0.3); padding: 1.5rem; border-radius: 8px; border: 2px solid rgba(76, 175, 80, 0.4);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                            <h5 style="color: #4caf50; margin: 0; font-size: 1.1rem;">@BlockchainBull</h5>
                            <span style="background: rgba(76, 175, 80, 0.2); color: #4caf50; padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.8rem; font-weight: bold;">87% Match</span>
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <div style="color: rgba(255, 255, 255, 0.9); margin-bottom: 0.5rem;">📱 1.4M followers • 🎯 79% accuracy</div>
                            <div style="color: #00ff88; font-weight: bold;">+1,650% avg gain</div>
                        </div>
                        <div style="color: rgba(255, 255, 255, 0.8); font-size: 0.9rem;">
                            <strong>Why Similar:</strong> Technical analysis focus, similar risk profile, complementary market insights
                        </div>
                    </div>

                    <!-- Similar KOL 3 -->
                    <div style="background: rgba(0, 0, 0, 0.3); padding: 1.5rem; border-radius: 8px; border: 2px solid rgba(244, 67, 54, 0.4);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                            <h5 style="color: #f44336; margin: 0; font-size: 1.1rem;">@DeFiMaster</h5>
                            <span style="background: rgba(244, 67, 54, 0.2); color: #f44336; padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.8rem; font-weight: bold;">83% Match</span>
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <div style="color: rgba(255, 255, 255, 0.9); margin-bottom: 0.5rem;">📱 980K followers • 🎯 68% accuracy</div>
                            <div style="color: #00ff88; font-weight: bold;">+2,340% avg gain</div>
                        </div>
                        <div style="color: rgba(255, 255, 255, 0.8); font-size: 0.9rem;">
                            <strong>Why Similar:</strong> DeFi specialization, early trend identification, similar community engagement
                        </div>
                    </div>
                </div>

                <div style="background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 8px; margin-top: 1.5rem;">
                    <p style="color: rgba(255, 255, 255, 0.8); margin: 0;">
                        <strong>💡 AI Recommendation:</strong> @{kol_name} shows strong performance in their niche. Consider following @CryptoAnalyst (91% similarity) for diversified signals and @BlockchainBull for technical confirmation.
                    </p>
                </div>
            </div>
            """
        else:
            return f"""
            <div class="professional-container">
                <h3 style="color: #764ba2; margin-bottom: 1rem;">🔍 Analysis Query: "{message}"</h3>
                <div style="background: rgba(255, 255, 255, 0.05); padding: 1.5rem; border-radius: 8px;">
                    <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 1rem;">
                        Processing your request with advanced AI analytics...
                    </p>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <div style="background: rgba(102, 126, 234, 0.1); padding: 1rem; border-radius: 6px;">
                            <h4 style="color: #667eea; margin: 0 0 0.5rem 0;">📊 Available Analysis</h4>
                            <ul style="color: rgba(255, 255, 255, 0.7); font-size: 0.9rem; margin: 0; padding-left: 1rem;">
                                <li>KOL Performance Tables</li>
                                <li>Contribution Attribution</li>
                                <li>Token Deep Dives</li>
                            </ul>
                        </div>
                        <div style="background: rgba(0, 255, 136, 0.1); padding: 1rem; border-radius: 6px;">
                            <h4 style="color: #00ff88; margin: 0 0 0.5rem 0;">💎 Supported Tokens</h4>
                            <ul style="color: rgba(255, 255, 255, 0.7); font-size: 0.9rem; margin: 0; padding-left: 1rem;">
                                <li>PEPE, DOGE, WOJAK</li>
                                <li>BTC, ETH, SOL</li>
                                <li>DEX Meme Coins</li>
                            </ul>
                        </div>
                        <div style="background: rgba(156, 39, 176, 0.1); padding: 1rem; border-radius: 6px;">
                            <h4 style="color: #9c27b0; margin: 0 0 0.5rem 0;">🔍 Try These Examples</h4>
                            <ul style="color: rgba(255, 255, 255, 0.7); font-size: 0.9rem; margin: 0; padding-left: 1rem;">
                                <li>"@YourKOL performance table"</li>
                                <li>"Compare @AnyKOL vs similar"</li>
                                <li>"Find similar KOLs to @Username"</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            """

# Create the professional interface
with gr.Blocks(
    title="ShillRank Agent - Professional",
    theme=gr.themes.Base(),
    css=PROFESSIONAL_CSS
) as demo:
    
    # Header
    gr.HTML(create_professional_header())
    
    # Quick Examples Section (prominent placement) - PURPLE BACKGROUND
    with gr.Group():
        gr.HTML("""
        <div class="professional-container" style="background: linear-gradient(135deg, rgba(118, 75, 162, 0.4) 0%, rgba(102, 126, 234, 0.4) 100%); border: 2px solid rgba(118, 75, 162, 0.6); margin-bottom: 1.5rem;">
            <h3 style="color: #ffffff; margin-bottom: 1rem; text-align: center; font-weight: 700;">💡 Try These Powerful Queries</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 1rem;">
                <div style="background: rgba(102, 126, 234, 0.35); border: 2px solid rgba(102, 126, 234, 0.6); border-radius: 12px; padding: 1.5rem; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);" onclick="document.querySelector('textarea').value='@CryptoWhale performance table'; document.querySelector('textarea').dispatchEvent(new Event('input'));">
                    <h4 style="color: #ffffff; margin: 0 0 0.5rem 0; font-weight: 700; font-size: 1.1rem;">📊 KOL Performance Analysis</h4>
                    <p style="color: #ffffff; font-size: 1rem; margin: 0; font-weight: 600;">"@CryptoWhale performance table"</p>
                    <p style="color: rgba(255, 255, 255, 0.95); font-size: 0.9rem; margin: 0.5rem 0 0 0;">View detailed trading performance with profit/loss data</p>
                </div>
                <div style="background: rgba(118, 75, 162, 0.35); border: 2px solid rgba(118, 75, 162, 0.6); border-radius: 12px; padding: 1.5rem; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(118, 75, 162, 0.3);" onclick="document.querySelector('textarea').value='PEPE contribution analysis'; document.querySelector('textarea').dispatchEvent(new Event('input'));">
                    <h4 style="color: #ffffff; margin: 0 0 0.5rem 0; font-weight: 700; font-size: 1.1rem;">🎯 Contribution Attribution</h4>
                    <p style="color: #ffffff; font-size: 1rem; margin: 0; font-weight: 600;">"PEPE contribution analysis"</p>
                    <p style="color: rgba(255, 255, 255, 0.95); font-size: 0.9rem; margin: 0.5rem 0 0 0;">See which KOLs drove the price pump with percentages</p>
                </div>
                <div style="background: rgba(156, 39, 176, 0.35); border: 2px solid rgba(156, 39, 176, 0.6); border-radius: 12px; padding: 1.5rem; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(156, 39, 176, 0.3);" onclick="document.querySelector('textarea').value='Compare @CryptoWhale vs similar KOLs'; document.querySelector('textarea').dispatchEvent(new Event('input'));">
                    <h4 style="color: #ffffff; margin: 0 0 0.5rem 0; font-weight: 700; font-size: 1.1rem;">🔍 KOL Comparison</h4>
                    <p style="color: #ffffff; font-size: 1rem; margin: 0; font-weight: 600;">"Compare @CryptoWhale vs similar KOLs"</p>
                    <p style="color: rgba(255, 255, 255, 0.95); font-size: 0.9rem; margin: 0.5rem 0 0 0;">Find and compare similar influencers for better decisions</p>
                </div>
            </div>
        </div>
        """)

    # Main content area (full width, no sidebar)
    with gr.Column():
        msg_input = gr.Textbox(
            label="🔍 Enter your analysis query",
            placeholder="Try: '@CryptoWhale performance table' or 'PEPE contribution analysis'",
            lines=2
        )

        with gr.Row():
            send_btn = gr.Button("🚀 Analyze", variant="primary")
            clear_btn = gr.Button("🗑️ Clear", variant="secondary")

        output = gr.HTML(label="📊 Analysis Results")
    
    # Simplified feature description
    gr.HTML("""
    <div style="text-align: center; margin: 1rem 0;">
        <h3 style="color: rgba(255, 255, 255, 0.8); font-size: 1.1rem; margin: 0;">
            🚀 Complete Feature Set: KOL Tracking • Attribution Analysis • DEX & Meme Coins • Visual Analytics • Comparison & Research • AI Assistant
        </h3>
    </div>
    """)



    # Keep the original examples as backup
    gr.Examples(
        examples=[
            "@CryptoWhale performance table",
            "PEPE contribution analysis",
            "WOJAK DEX meme analysis",
            "@ElonMusk mentioned coins data",
            "TURBO pump attribution breakdown",
            "Show me charts and analytics"
        ],
        inputs=msg_input,
        label="📋 Additional Examples",
        visible=False
    )
    
    # Footer
    gr.HTML("""
    <div class="professional-container" style="text-align: center; margin-top: 2rem;">
        <p><strong>🏆 Agent Demo Marathon - Track 3 Submission</strong></p>
        <p>🤖 Powered by Advanced AI • 📊 Real-time Data • 🔒 Professional Grade</p>
        <p>⚠️ <em>For educational and research purposes only. Not financial advice.</em></p>
    </div>
    """)
    
    # Event handlers
    send_btn.click(
        fn=shillrank_agent_professional,
        inputs=[msg_input],
        outputs=[output]
    )
    
    msg_input.submit(
        fn=shillrank_agent_professional,
        inputs=[msg_input],
        outputs=[output]
    )
    
    clear_btn.click(
        fn=lambda: ("", ""),
        outputs=[msg_input, output]
    )

if __name__ == "__main__":
    demo.launch()
