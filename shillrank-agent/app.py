import gradio as gr

def shillrank_agent(message):
    """最简单的Agent函数"""
    if not message or not message.strip():
        return "Please enter a message to get started!"
    
    message = message.lower().strip()
    
    # 简单的关键词匹配
    if any(word in message for word in ["hello", "hi", "start", "what can you do"]):
        return """🤖 **Welcome to ShillRank Agent!**

I'm an AI assistant specialized in cryptocurrency KOL influence analysis.

**What I can help with:**
• Analyze KOL influence on cryptocurrencies  
• Provide market insights
• Generate influence rankings

**Try asking:**
• "Analyze PEPE coin"
• "BTC analysis" 
• "Help with crypto"

This is a demo for the Agent Demo Marathon showcasing conversational AI capabilities."""

    elif "pepe" in message:
        return """📊 **PEPE Coin KOL Influence Leaderboard**

🏆 **Top 5 KOL Rankings:**

🥇 **@CryptoWhale** (Score: 87.3)
   • Mentions: 12 tweets | Engagement: 25.3K avg
   • Price Impact: +2.3% | Accuracy: 78%
   • Historical Coins: BTC (+15%), ETH (+8%), SHIB (+45%)

🥈 **@PepeMaster** (Score: 84.1)
   • Mentions: 8 tweets | Engagement: 18.9K avg
   • Price Impact: ****% | Accuracy: 82%
   • Historical Coins: DOGE (+23%), PEPE (+67%), FLOKI (+34%)

🥉 **@MemeKingCrypto** (Score: 81.7)
   • Mentions: 15 tweets | Engagement: 16.2K avg
   • Price Impact: +1.5% | Accuracy: 71%
   • Historical Coins: SHIB (+89%), PEPE (+45%), BONK (+156%)

4️⃣ **@AltcoinGuru** (Score: 79.2)
   • Mentions: 6 tweets | Engagement: 22.1K avg
   • Price Impact: +1.2% | Accuracy: 85%
   • Historical Coins: SOL (+34%), AVAX (+28%), PEPE (+23%)

5️⃣ **@DegenTrader** (Score: 76.8)
   • Mentions: 20 tweets | Engagement: 12.5K avg
   • Price Impact: +0.9% | Accuracy: 69%
   • Historical Coins: PEPE (+78%), WOJAK (+123%), TURBO (+67%)

📈 **PEPE Performance Summary:**
• Total KOL Mentions: 61 in 7 days
• Average Sentiment: 0.73 (Bullish)
• Price Correlation: 0.68 (Strong)
• Community Engagement: 94.2K total interactions

⚠️ Demo data showcasing advanced Agent analytics capabilities."""

    elif any(word in message for word in ["btc", "bitcoin"]):
        return """💰 **Bitcoin (BTC) KOL Influence Leaderboard**

📈 **Market Overview:**
• Current Price: $43,250 (+2.3%) | Market Cap: $847B | Volume: $28.5B

🏆 **Top 10 Bitcoin KOL Rankings:**

🥇 **@BitcoinGuru** (Score: 92.5)
   • Accuracy: 89% | Engagement: 45.2K avg | Impact: +2.8%
   • Recent Calls: BTC $45K ✅, ETH $2.8K ✅, SOL $120 ✅

🥈 **@SatoshiWhale** (Score: 90.1)
   • Accuracy: 87% | Engagement: 38.7K avg | Impact: +2.5%
   • Recent Calls: BTC $42K ✅, LINK $18 ✅, ADA $0.65 ❌

🥉 **@CryptoKing** (Score: 88.3)
   • Accuracy: 85% | Engagement: 52.1K avg | Impact: +2.2%
   • Recent Calls: BTC $44K ✅, MATIC $1.2 ✅, DOT $8.5 ✅

4️⃣ **@BlockchainBull** (Score: 86.7)
   • Accuracy: 83% | Engagement: 29.8K avg | Impact: +1.9%
   • Recent Calls: BTC $43.5K ✅, AVAX $45 ❌, ATOM $12 ✅

5️⃣ **@DigitalGoldHODL** (Score: 84.2)
   • Accuracy: 81% | Engagement: 33.4K avg | Impact: +1.7%
   • Recent Calls: BTC $46K ⏳, XRP $0.8 ✅, LTC $85 ❌

6️⃣ **@InstitutionalCrypto** (Score: 82.8)
   • Accuracy: 88% | Engagement: 25.6K avg | Impact: +1.5%
   • Recent Calls: BTC $41K ✅, ETH $2.6K ✅, BNB $320 ✅

7️⃣ **@TechnicalTrader** (Score: 81.4)
   • Accuracy: 79% | Engagement: 41.2K avg | Impact: +1.4%
   • Recent Calls: BTC $42.8K ✅, UNI $8.5 ❌, AAVE $105 ✅

8️⃣ **@MacroMoney** (Score: 79.9)
   • Accuracy: 76% | Engagement: 36.8K avg | Impact: +1.2%
   • Recent Calls: BTC $44.5K ⏳, GOLD correlation ✅, DXY analysis ✅

9️⃣ **@OnChainAnalyst** (Score: 78.5)
   • Accuracy: 82% | Engagement: 19.3K avg | Impact: +1.1%
   • Recent Calls: BTC accumulation ✅, Whale activity ✅, Exchange flows ✅

🔟 **@CryptoProfessor** (Score: 77.1)
   • Accuracy: 74% | Engagement: 28.7K avg | Impact: +0.9%
   • Recent Calls: BTC $43K ✅, Market cycle analysis ✅, Alt season ❌

📊 **BTC Influence Summary:**
• Total KOL Mentions: 156 in 7 days
• Bullish Sentiment: 78% | Bearish: 15% | Neutral: 7%
• Average Price Target: $47,200 (next 30 days)
• Institutional Interest: Very High

🤖 **Agent Analysis:** Strong consensus among top KOLs with institutional backing driving positive sentiment."""

    elif any(word in message for word in ["help", "function", "capability"]):
        return """🆘 **ShillRank Agent - Help Guide**

**Core Agent Functions:**
1. 🔍 **KOL Influence Analysis** - Analyze crypto opinion leaders
2. 💹 **Market Data Queries** - Get real-time price information  
3. 📊 **Trend Analysis** - Understand market movements
4. 📈 **Influence Rankings** - Compare KOL performance

**How to Use:**
• Ask questions in natural language
• Mention specific cryptocurrencies by name
• Request analysis or market data

**Example Queries:**
• "Analyze DOGE influence"
• "What's happening with ETH?"
• "Show me crypto market trends"

🚀 **Agent Demo Features:**
• Natural language understanding
• Intent recognition and context processing
• Intelligent response generation
• Real-world task execution simulation

This demonstrates the power of conversational AI agents!"""

    elif any(word in message for word in ["doge", "dogecoin"]):
        return """🐕 **Dogecoin (DOGE) KOL Analysis**

📊 **Influence Metrics:**
• Top KOL: @DogeWhale (Score: 84.2)
• Community Mentions: 25 posts/day
• Sentiment: Very Positive (0.81)
• Price Correlation: ****%

🚀 **Recent Activity:**
• Elon Musk mention impact: +12%
• Community engagement: High
• Meme virality factor: Strong

🤖 **Agent Insight:** DOGE shows strong community-driven influence patterns with high social media correlation."""

    elif any(word in message for word in ["eth", "ethereum"]):
        return """⚡ **Ethereum (ETH) KOL Analysis**

📈 **Technical Analysis:**
• Price: $2,580 (****%) | Market Cap: $310B | DeFi TVL: $25.8B

🔍 **KOL Landscape:**
• Top Influencer: @EthereumGuru
• Developer Sentiment: Bullish
• Institutional Interest: Growing
• Upgrade Hype: Moderate

🤖 **Agent Analysis:** ETH shows strong technical fundamentals with positive developer and institutional KOL sentiment."""

    elif any(word in message for word in ["ranking", "leaderboard", "top kol", "best kol"]):
        return """🏆 **Global KOL Influence Leaderboard**

📊 **Overall Top 15 Crypto KOLs (All Coins)**

🥇 **@BitcoinGuru** - Score: 92.5
   📈 Recent Hits: BTC (+15%), ETH (+12%), SOL (+34%)
   🎯 Accuracy: 89% | 📱 Followers: 2.1M

🥈 **@SatoshiWhale** - Score: 90.1
   📈 Recent Hits: BTC (+18%), LINK (+45%), AVAX (+28%)
   🎯 Accuracy: 87% | 📱 Followers: 1.8M

🥉 **@CryptoKing** - Score: 88.3
   📈 Recent Hits: MATIC (+67%), DOT (+23%), BTC (+8%)
   🎯 Accuracy: 85% | 📱 Followers: 3.2M

4️⃣ **@DeFiWhale** - Score: 87.9
   📈 Recent Hits: UNI (+89%), AAVE (+45%), COMP (+78%)
   🎯 Accuracy: 91% | 📱 Followers: 890K

5️⃣ **@AltcoinSherpa** - Score: 86.4
   📈 Recent Hits: PEPE (+156%), SHIB (+67%), FLOKI (+234%)
   🎯 Accuracy: 78% | 📱 Followers: 1.5M

6️⃣ **@BlockchainBull** - Score: 86.7
   📈 Recent Hits: AVAX (+34%), ATOM (+56%), NEAR (+78%)
   🎯 Accuracy: 83% | 📱 Followers: 1.2M

7️⃣ **@MemeKingCrypto** - Score: 84.1
   📈 Recent Hits: DOGE (+45%), PEPE (+123%), BONK (+289%)
   🎯 Accuracy: 71% | 📱 Followers: 2.8M

8️⃣ **@InstitutionalCrypto** - Score: 82.8
   📈 Recent Hits: BTC (+12%), ETH (+15%), BNB (+23%)
   🎯 Accuracy: 88% | 📱 Followers: 567K

9️⃣ **@TechnicalTrader** - Score: 81.4
   📈 Recent Hits: SOL (+67%), ADA (+34%), XRP (+28%)
   🎯 Accuracy: 79% | 📱 Followers: 1.9M

🔟 **@OnChainAnalyst** - Score: 78.5
   📈 Recent Hits: Whale movements ✅, Exchange flows ✅, BTC accumulation ✅
   🎯 Accuracy: 82% | 📱 Followers: 743K

1️⃣1️⃣ **@CryptoProfessor** - Score: 77.1
   📈 Recent Hits: Market cycles ✅, Alt season timing ✅, BTC dominance ✅
   🎯 Accuracy: 74% | 📱 Followers: 1.1M

1️⃣2️⃣ **@DegenTrader** - Score: 76.8
   📈 Recent Hits: WOJAK (+234%), TURBO (+156%), PEPE (+89%)
   🎯 Accuracy: 69% | 📱 Followers: 2.3M

1️⃣3️⃣ **@MacroMoney** - Score: 75.2
   📈 Recent Hits: Fed policy impact ✅, DXY correlation ✅, Gold/BTC ratio ✅
   🎯 Accuracy: 76% | 📱 Followers: 892K

1️⃣4️⃣ **@NFTWhale** - Score: 73.9
   📈 Recent Hits: BLUR (+78%), APE (+45%), LOOKS (+123%)
   🎯 Accuracy: 68% | 📱 Followers: 1.4M

1️⃣5️⃣ **@Layer1Alpha** - Score: 72.6
   📈 Recent Hits: ATOM (+56%), DOT (+34%), ALGO (+67%)
   🎯 Accuracy: 72% | 📱 Followers: 654K

📊 **Leaderboard Stats:**
• Total Tracked KOLs: 500+
• Average Accuracy: 78.3%
• Total Followers: 25.8M
• Weekly Analysis Updates: 1,247

🔥 **Hottest Calls This Week:**
1. PEPE: +156% (Called by @AltcoinSherpa)
2. WOJAK: +234% (Called by @DegenTrader)
3. BONK: +289% (Called by @MemeKingCrypto)
4. UNI: +89% (Called by @DeFiWhale)
5. SOL: +67% (Called by @TechnicalTrader)

🤖 **Agent Insight:** Meme coins dominating this week with exceptional KOL prediction accuracy."""

    else:
        return f"""🤔 **Processing your query:** "{message}"

As a cryptocurrency KOL influence analysis specialist, I can help you with:

🔍 **Available Analysis:**
• "Analyze [COIN] influence" - Get detailed KOL reports
• "Help" - See all available functions  
• "Hello" - Get started with the agent

💡 **Supported Cryptocurrencies:**
• Bitcoin (BTC) • Ethereum (ETH) • Dogecoin (DOGE) • PEPE

🤖 **Agent Capabilities:**
• Natural language understanding
• Intent recognition and parameter extraction
• Contextual response generation
• Intelligent task routing and execution

Ready to analyze crypto KOL influence? Try asking about a specific cryptocurrency!"""

# 创建最简单的Interface
demo = gr.Interface(
    fn=shillrank_agent,
    inputs=gr.Textbox(
        label="Ask ShillRank Agent", 
        placeholder="e.g., Hello, what can you do?",
        lines=2
    ),
    outputs=gr.Textbox(
        label="Agent Response",
        lines=10
    ),
    title="🤖 ShillRank Agent",
    description="**Cryptocurrency KOL Influence Analysis Assistant**\n\n*Agent Demo Marathon - Track 3 Submission*\n\nConversational AI for analyzing crypto opinion leader influence and market impact.",
    examples=[
        "Hello, what can you do?",
        "Show me the global KOL leaderboard",
        "Analyze PEPE coin influence",
        "BTC market analysis with top KOLs",
        "Help me understand your functions",
        "Analyze DOGE influence",
        "Top KOL rankings"
    ],
    theme=gr.themes.Soft(),
    allow_flagging="never"
)

if __name__ == "__main__":
    demo.launch()
