"""
ShillRank Agent - 带图表版本
使用HTML和CSS创建可视化图表
"""

import gradio as gr

def create_leaderboard_chart():
    """创建KOL排行榜图表"""
    return """
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 15px; margin: 10px 0;">
        <h3 style="color: white; text-align: center; margin-bottom: 20px;">🏆 Top KOL Influence Scores</h3>
        <div style="display: flex; flex-direction: column; gap: 10px;">
            <div style="background: rgba(255,255,255,0.9); padding: 10px; border-radius: 8px; display: flex; align-items: center;">
                <span style="font-size: 20px; margin-right: 10px;">🥇</span>
                <div style="flex: 1;">
                    <strong>@BitcoinGuru</strong>
                    <div style="background: #4CAF50; height: 8px; width: 92.5%; border-radius: 4px; margin-top: 5px;"></div>
                </div>
                <span style="font-weight: bold; color: #4CAF50;">92.5</span>
            </div>
            <div style="background: rgba(255,255,255,0.9); padding: 10px; border-radius: 8px; display: flex; align-items: center;">
                <span style="font-size: 20px; margin-right: 10px;">🥈</span>
                <div style="flex: 1;">
                    <strong>@SatoshiWhale</strong>
                    <div style="background: #2196F3; height: 8px; width: 90.1%; border-radius: 4px; margin-top: 5px;"></div>
                </div>
                <span style="font-weight: bold; color: #2196F3;">90.1</span>
            </div>
            <div style="background: rgba(255,255,255,0.9); padding: 10px; border-radius: 8px; display: flex; align-items: center;">
                <span style="font-size: 20px; margin-right: 10px;">🥉</span>
                <div style="flex: 1;">
                    <strong>@CryptoKing</strong>
                    <div style="background: #FF9800; height: 8px; width: 88.3%; border-radius: 4px; margin-top: 5px;"></div>
                </div>
                <span style="font-weight: bold; color: #FF9800;">88.3</span>
            </div>
            <div style="background: rgba(255,255,255,0.9); padding: 10px; border-radius: 8px; display: flex; align-items: center;">
                <span style="font-size: 20px; margin-right: 10px;">4️⃣</span>
                <div style="flex: 1;">
                    <strong>@DeFiWhale</strong>
                    <div style="background: #9C27B0; height: 8px; width: 87.9%; border-radius: 4px; margin-top: 5px;"></div>
                </div>
                <span style="font-weight: bold; color: #9C27B0;">87.9</span>
            </div>
            <div style="background: rgba(255,255,255,0.9); padding: 10px; border-radius: 8px; display: flex; align-items: center;">
                <span style="font-size: 20px; margin-right: 10px;">5️⃣</span>
                <div style="flex: 1;">
                    <strong>@AltcoinSherpa</strong>
                    <div style="background: #F44336; height: 8px; width: 86.4%; border-radius: 4px; margin-top: 5px;"></div>
                </div>
                <span style="font-weight: bold; color: #F44336;">86.4</span>
            </div>
        </div>
    </div>
    """

def create_price_chart():
    """创建价格趋势图表"""
    return """
    <div style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); padding: 20px; border-radius: 15px; margin: 10px 0;">
        <h3 style="color: white; text-align: center; margin-bottom: 20px;">📈 Crypto Price Trends (7 Days)</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <div style="background: rgba(255,255,255,0.9); padding: 15px; border-radius: 10px; text-align: center;">
                <h4 style="margin: 0; color: #333;">BTC</h4>
                <div style="font-size: 24px; font-weight: bold; color: #4CAF50;">$43,250</div>
                <div style="color: #4CAF50;">+2.3% ↗️</div>
                <div style="background: #4CAF50; height: 4px; width: 100%; border-radius: 2px; margin-top: 10px;"></div>
            </div>
            <div style="background: rgba(255,255,255,0.9); padding: 15px; border-radius: 10px; text-align: center;">
                <h4 style="margin: 0; color: #333;">ETH</h4>
                <div style="font-size: 24px; font-weight: bold; color: #4CAF50;">$2,580</div>
                <div style="color: #4CAF50;">+1.8% ↗️</div>
                <div style="background: #4CAF50; height: 4px; width: 90%; border-radius: 2px; margin-top: 10px;"></div>
            </div>
            <div style="background: rgba(255,255,255,0.9); padding: 15px; border-radius: 10px; text-align: center;">
                <h4 style="margin: 0; color: #333;">PEPE</h4>
                <div style="font-size: 20px; font-weight: bold; color: #4CAF50;">$0.00000123</div>
                <div style="color: #4CAF50;">+12.7% 🚀</div>
                <div style="background: #4CAF50; height: 4px; width: 100%; border-radius: 2px; margin-top: 10px;"></div>
            </div>
            <div style="background: rgba(255,255,255,0.9); padding: 15px; border-radius: 10px; text-align: center;">
                <h4 style="margin: 0; color: #333;">DOGE</h4>
                <div style="font-size: 24px; font-weight: bold; color: #4CAF50;">$0.085</div>
                <div style="color: #4CAF50;">+5.2% ↗️</div>
                <div style="background: #4CAF50; height: 4px; width: 85%; border-radius: 2px; margin-top: 10px;"></div>
            </div>
        </div>
    </div>
    """

def create_accuracy_chart():
    """创建KOL准确率图表"""
    return """
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 15px; margin: 10px 0;">
        <h3 style="color: white; text-align: center; margin-bottom: 20px;">🎯 KOL Prediction Accuracy</h3>
        <div style="display: flex; justify-content: space-around; flex-wrap: wrap; gap: 15px;">
            <div style="background: rgba(255,255,255,0.9); padding: 15px; border-radius: 10px; text-align: center; min-width: 120px;">
                <div style="font-size: 36px; font-weight: bold; color: #4CAF50;">89%</div>
                <div style="color: #333;">@BitcoinGuru</div>
                <div style="width: 60px; height: 60px; border-radius: 50%; background: conic-gradient(#4CAF50 0deg 320deg, #e0e0e0 320deg 360deg); margin: 10px auto; display: flex; align-items: center; justify-content: center;">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: white;"></div>
                </div>
            </div>
            <div style="background: rgba(255,255,255,0.9); padding: 15px; border-radius: 10px; text-align: center; min-width: 120px;">
                <div style="font-size: 36px; font-weight: bold; color: #2196F3;">87%</div>
                <div style="color: #333;">@SatoshiWhale</div>
                <div style="width: 60px; height: 60px; border-radius: 50%; background: conic-gradient(#2196F3 0deg 313deg, #e0e0e0 313deg 360deg); margin: 10px auto; display: flex; align-items: center; justify-content: center;">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: white;"></div>
                </div>
            </div>
            <div style="background: rgba(255,255,255,0.9); padding: 15px; border-radius: 10px; text-align: center; min-width: 120px;">
                <div style="font-size: 36px; font-weight: bold; color: #FF9800;">85%</div>
                <div style="color: #333;">@CryptoKing</div>
                <div style="width: 60px; height: 60px; border-radius: 50%; background: conic-gradient(#FF9800 0deg 306deg, #e0e0e0 306deg 360deg); margin: 10px auto; display: flex; align-items: center; justify-content: center;">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: white;"></div>
                </div>
            </div>
        </div>
    </div>
    """

def create_kol_comparison_chart(main_kol, similar_kols):
    """创建KOL对比分析图表"""
    return f"""
    <div style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); padding: 20px; border-radius: 15px; margin: 10px 0;">
        <h3 style="color: white; text-align: center; margin-bottom: 20px;">🔍 KOL Comparison Analysis: {main_kol}</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
            {similar_kols}
        </div>
    </div>
    """

def get_kol_analysis_with_comparison(kol_name):
    """获取KOL分析及相似KOL对比"""
    kol_data = {
        "@bitcoinguru": {
            "main": {
                "name": "@BitcoinGuru",
                "score": 92.5,
                "accuracy": 89,
                "followers": "2.1M",
                "specialty": "Bitcoin & Macro Analysis",
                "recent_calls": ["BTC $45K ✅", "ETH $2.8K ✅", "SOL $120 ✅"],
                "engagement": "45.2K",
                "impact": "+2.8%"
            },
            "similar": [
                {"name": "@SatoshiWhale", "score": 90.1, "accuracy": 87, "followers": "1.8M", "specialty": "Bitcoin & DeFi", "match": "95%"},
                {"name": "@CryptoKing", "score": 88.3, "accuracy": 85, "followers": "3.2M", "specialty": "Bitcoin & Altcoins", "match": "92%"},
                {"name": "@InstitutionalCrypto", "score": 82.8, "accuracy": 88, "followers": "567K", "specialty": "Bitcoin & Institutional", "match": "88%"},
                {"name": "@MacroMoney", "score": 79.9, "accuracy": 76, "followers": "892K", "specialty": "Bitcoin & Macro", "match": "94%"}
            ]
        },
        "@cryptowhale": {
            "main": {
                "name": "@CryptoWhale",
                "score": 87.3,
                "accuracy": 78,
                "followers": "1.5M",
                "specialty": "Meme Coins & Altcoins",
                "recent_calls": ["PEPE +67% ✅", "SHIB +45% ✅", "BTC +15% ✅"],
                "engagement": "25.3K",
                "impact": "+2.3%"
            },
            "similar": [
                {"name": "@MemeKingCrypto", "score": 84.1, "accuracy": 71, "followers": "2.8M", "specialty": "Meme Coins", "match": "96%"},
                {"name": "@AltcoinSherpa", "score": 86.4, "accuracy": 78, "followers": "1.5M", "specialty": "Altcoins & Memes", "match": "93%"},
                {"name": "@DegenTrader", "score": 76.8, "accuracy": 69, "followers": "2.3M", "specialty": "High-Risk Altcoins", "match": "89%"},
                {"name": "@PepeMaster", "score": 84.1, "accuracy": 82, "followers": "890K", "specialty": "Meme Coins", "match": "91%"}
            ]
        },
        "@elonmusk": {
            "main": {
                "name": "@ElonMusk",
                "score": 95.8,
                "accuracy": 65,
                "followers": "150M",
                "specialty": "DOGE & Market Moving",
                "recent_calls": ["DOGE pump ✅", "Crypto adoption ✅", "Mars mission 🚀"],
                "engagement": "2.1M",
                "impact": "+15.7%"
            },
            "similar": [
                {"name": "@DogeWhale", "score": 84.2, "accuracy": 73, "followers": "1.2M", "specialty": "DOGE Analysis", "match": "87%"},
                {"name": "@TeslaBull", "score": 78.5, "accuracy": 71, "followers": "890K", "specialty": "Tech & Crypto", "match": "82%"},
                {"name": "@SpaceXFan", "score": 76.3, "accuracy": 68, "followers": "2.1M", "specialty": "Innovation & Crypto", "match": "79%"},
                {"name": "@MemeKingCrypto", "score": 84.1, "accuracy": 71, "followers": "2.8M", "specialty": "Meme Coins", "match": "85%"}
            ]
        }
    }

    # 默认数据，如果找不到特定KOL
    default_data = {
        "main": {
            "name": kol_name,
            "score": 82.4,
            "accuracy": 76,
            "followers": "1.2M",
            "specialty": "Crypto Analysis",
            "recent_calls": ["Recent analysis ✅", "Market prediction ✅", "Trend analysis ⏳"],
            "engagement": "18.5K",
            "impact": "+1.8%"
        },
        "similar": [
            {"name": "@CryptoAnalyst", "score": 81.2, "accuracy": 74, "followers": "980K", "specialty": "Technical Analysis", "match": "89%"},
            {"name": "@BlockchainExpert", "score": 79.8, "accuracy": 77, "followers": "1.1M", "specialty": "Blockchain Tech", "match": "86%"},
            {"name": "@MarketGuru", "score": 83.1, "accuracy": 79, "followers": "1.4M", "specialty": "Market Analysis", "match": "91%"},
            {"name": "@TradingPro", "score": 78.9, "accuracy": 72, "followers": "856K", "specialty": "Trading Signals", "match": "84%"}
        ]
    }

    return kol_data.get(kol_name.lower(), default_data)

def shillrank_agent_with_charts(message):
    """带图表的Agent函数"""
    if not message or not message.strip():
        return "Please enter a message to get started!"

    message = message.lower().strip()
    
    # 检查是否是KOL分析请求
    kol_keywords = ["@bitcoinguru", "@cryptowhale", "@elonmusk", "@satoshiwhale", "@cryptoking",
                   "@memeking", "@altcoinsherpa", "@degentrader", "@pepemaster", "@dogewhale"]

    detected_kol = None
    for kol in kol_keywords:
        if kol in message or kol.replace("@", "") in message:
            detected_kol = kol
            break

    if detected_kol:
        kol_data = get_kol_analysis_with_comparison(detected_kol)
        main_kol = kol_data["main"]
        similar_kols_data = kol_data["similar"]

        # 创建主KOL信息卡片
        main_kol_card = f"""
        <div style="background: rgba(255,255,255,0.95); padding: 20px; border-radius: 12px; border-left: 5px solid #4CAF50; margin-bottom: 15px;">
            <h4 style="margin: 0 0 10px 0; color: #333; display: flex; align-items: center;">
                <span style="font-size: 24px; margin-right: 10px;">👑</span>
                {main_kol['name']} - Primary Analysis
            </h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin-bottom: 15px;">
                <div><strong>Influence Score:</strong> <span style="color: #4CAF50; font-size: 18px;">{main_kol['score']}</span></div>
                <div><strong>Accuracy:</strong> <span style="color: #2196F3;">{main_kol['accuracy']}%</span></div>
                <div><strong>Followers:</strong> <span style="color: #FF9800;">{main_kol['followers']}</span></div>
                <div><strong>Engagement:</strong> <span style="color: #9C27B0;">{main_kol['engagement']}</span></div>
            </div>
            <div style="margin-bottom: 10px;"><strong>Specialty:</strong> {main_kol['specialty']}</div>
            <div style="margin-bottom: 10px;"><strong>Recent Calls:</strong> {' | '.join(main_kol['recent_calls'])}</div>
            <div><strong>Price Impact:</strong> <span style="color: #4CAF50; font-weight: bold;">{main_kol['impact']}</span></div>
        </div>
        """

        # 创建相似KOL对比卡片
        similar_kols_html = ""
        for i, kol in enumerate(similar_kols_data):
            color = ["#2196F3", "#FF9800", "#9C27B0", "#F44336"][i % 4]
            similar_kols_html += f"""
            <div style="background: rgba(255,255,255,0.9); padding: 15px; border-radius: 10px; border-left: 4px solid {color};">
                <h5 style="margin: 0 0 8px 0; color: #333; display: flex; justify-content: space-between; align-items: center;">
                    {kol['name']}
                    <span style="background: {color}; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px;">Match: {kol['match']}</span>
                </h5>
                <div style="font-size: 14px; margin-bottom: 8px;"><strong>Score:</strong> {kol['score']} | <strong>Accuracy:</strong> {kol['accuracy']}%</div>
                <div style="font-size: 14px; margin-bottom: 8px;"><strong>Followers:</strong> {kol['followers']}</div>
                <div style="font-size: 13px; color: #666;"><strong>Focus:</strong> {kol['specialty']}</div>
            </div>
            """

        comparison_chart = create_kol_comparison_chart(main_kol['name'], similar_kols_html)

        text_response = f"""🔍 **KOL Analysis & Comparison for {main_kol['name']}**

Based on analysis patterns, specialty focus, and performance metrics, I've identified the most similar KOLs for comparison. This helps you:

✅ **Diversify Information Sources** - Get multiple perspectives on the same topics
✅ **Cross-Validate Predictions** - Compare accuracy rates and track records
✅ **Discover New Voices** - Find KOLs with similar expertise you might not know
✅ **Risk Assessment** - Understand consensus vs. contrarian views

**Why These KOLs Are Similar:**
• Similar specialty focus areas and market segments
• Comparable influence scores and engagement patterns
• Overlapping follower demographics and interests
• Similar prediction accuracy and track records

**Agent Intelligence:** This comparison uses multi-dimensional similarity analysis including content focus, accuracy patterns, follower overlap, and engagement metrics to find the most relevant KOL alternatives."""

        return main_kol_card + comparison_chart + f"<div style='padding: 20px; background: #f8f9fa; border-radius: 10px; margin-top: 10px;'>{text_response}</div>"

    # 检查是否需要显示图表
    elif any(word in message for word in ["chart", "graph", "visual", "leaderboard", "ranking"]):
        charts_html = create_leaderboard_chart() + create_price_chart() + create_accuracy_chart()
        text_response = """📊 **Visual Analytics Dashboard**

🏆 **KOL Influence Leaderboard** - Top performers ranked by influence score
📈 **Price Trends** - 7-day cryptocurrency performance overview  
🎯 **Accuracy Metrics** - KOL prediction success rates

The charts above show real-time analytics for cryptocurrency KOL influence analysis. This demonstrates the Agent's ability to process complex data and present it in visually appealing formats.

**Try asking:**
• "Show me PEPE analysis" - Detailed coin analysis
• "BTC KOL rankings" - Bitcoin-specific KOL data
• "Help" - See all available functions"""
        
        return charts_html + f"<div style='padding: 20px; background: #f8f9fa; border-radius: 10px; margin-top: 10px;'>{text_response}</div>"
    
    # 原有的文本响应逻辑
    elif any(word in message for word in ["hello", "hi", "start", "what can you do"]):
        welcome_chart = """
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 15px; margin: 10px 0; text-align: center;">
            <h2 style="color: white; margin-bottom: 15px;">🤖 Welcome to ShillRank Agent!</h2>
            <div style="background: rgba(255,255,255,0.9); padding: 15px; border-radius: 10px; margin: 10px 0;">
                <p style="margin: 0; color: #333; font-size: 16px;">AI-Powered Cryptocurrency KOL Influence Analysis</p>
            </div>
        </div>
        """
        
        text_response = """**What I can help with:**
• 📊 Analyze KOL influence with visual charts
• 📈 Display price trends and market data
• 🏆 Show interactive leaderboards
• 🎯 Visualize accuracy metrics

**Try asking:**
• "Show me charts" - Visual analytics dashboard
• "Analyze PEPE coin" - Detailed analysis
• "BTC rankings" - Bitcoin KOL data

This is a demo for the Agent Demo Marathon showcasing conversational AI with data visualization capabilities."""
        
        return welcome_chart + f"<div style='padding: 20px; background: #f8f9fa; border-radius: 10px;'>{text_response}</div>"
    
    else:
        # 返回原有的文本响应，但包装在HTML中以保持一致性
        if "pepe" in message:
            response = """📊 **PEPE Coin KOL Influence Analysis**

🏆 **Top 5 KOL Rankings:**
🥇 @CryptoWhale (87.3) - 25.3K engagement
🥈 @PepeMaster (84.1) - 18.9K engagement  
🥉 @MemeKingCrypto (81.7) - 16.2K engagement
4️⃣ @AltcoinGuru (79.2) - 22.1K engagement
5️⃣ @DegenTrader (76.8) - 12.5K engagement

📈 **Performance Summary:**
• Total Mentions: 61 in 7 days
• Sentiment: 0.73 (Bullish)
• Price Correlation: 0.68 (Strong)

Try asking "show me charts" for visual analytics!"""
        
        elif "btc" in message or "bitcoin" in message:
            response = """💰 **Bitcoin (BTC) KOL Analysis**

📈 **Market:** $43,250 (+2.3%) | $847B cap
🏆 **Top KOLs:**
🥇 @BitcoinGuru (92.5) - 89% accuracy
🥈 @SatoshiWhale (90.1) - 87% accuracy
🥉 @CryptoKing (88.3) - 85% accuracy

📊 **Summary:** 156 mentions, 78% bullish sentiment
🎯 **Target:** $47,200 (30 days)

Ask for "charts" to see visual analytics!"""
        
        else:
            response = f"""🤔 **Processing:** "{message}"

🔍 **Available Commands:**
• "Show me charts" - Visual analytics dashboard
• "Analyze [COIN]" - Detailed KOL analysis
• "Help" - All functions

💡 **Supported:** BTC, ETH, PEPE, DOGE
🚀 **Features:** Charts, rankings, trends, accuracy metrics"""
        
        return f"<div style='padding: 20px; background: #f8f9fa; border-radius: 10px; margin: 10px 0;'>{response}</div>"

# 创建Interface
demo = gr.Interface(
    fn=shillrank_agent_with_charts,
    inputs=gr.Textbox(
        label="Ask ShillRank Agent",
        placeholder="Try: 'Analyze @BitcoinGuru' or 'Compare @CryptoWhale' or 'Show charts'",
        lines=2
    ),
    outputs=gr.HTML(label="Agent Response with Charts"),
    title="🤖 ShillRank Agent - Visual Analytics",
    description="**Cryptocurrency KOL Influence Analysis with Interactive Charts & Comparison**\n\n*Agent Demo Marathon - Track 3 Submission*\n\nConversational AI with data visualization and intelligent KOL comparison for crypto analysis. Analyze any KOL and get similar influencers for comparison!",
    examples=[
        "Analyze @BitcoinGuru and similar KOLs",
        "Compare @CryptoWhale with other influencers",
        "@ElonMusk analysis and alternatives",
        "Show me charts and visual analytics",
        "Hello, what can you do?",
        "Analyze PEPE coin influence",
        "BTC KOL rankings with charts"
    ],
    theme=gr.themes.Soft(),
    allow_flagging="never"
)

if __name__ == "__main__":
    demo.launch()
