"""
Agent 配置文件
"""

import os
from dotenv import load_dotenv

load_dotenv()

# Agent 基础配置
AGENT_NAME = "CryptoKOL Agent"
AGENT_VERSION = "1.0.0"
AGENT_DESCRIPTION = "智能加密货币KOL影响力分析助手"

# Hugging Face 配置
HUGGINGFACE_API_TOKEN = os.getenv("HUGGINGFACE_API_TOKEN", "")
DEFAULT_LLM_MODEL = "microsoft/DialoGPT-medium"
EMBEDDING_MODEL = "sentence-transformers/all-MiniLM-L6-v2"

# Agent 行为配置
MAX_ITERATIONS = 5
CONTEXT_WINDOW = 2048
MAX_NEW_TOKENS = 512
TEMPERATURE = 0.7

# 工具配置
ENABLE_COINGECKO_TOOL = True
ENABLE_TWITTER_TOOL = True
ENABLE_ANALYSIS_TOOL = True

# 缓存配置
ENABLE_MEMORY = True
MEMORY_TOKEN_LIMIT = 1500

# 响应配置
DEFAULT_LANGUAGE = "zh-CN"
RESPONSE_FORMAT = "markdown"

# 安全配置
MAX_MESSAGE_LENGTH = 1000
RATE_LIMIT_ENABLED = False

# 调试配置
VERBOSE_MODE = True
LOG_LEVEL = "INFO"
