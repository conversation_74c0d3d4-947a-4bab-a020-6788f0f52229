"""
KOL 知识库 - 向量存储和检索
"""

from typing import List, Dict, Any, Optional
import json
import os
from datetime import datetime

try:
    import chromadb
    from llama_index.vector_stores import ChromaVectorStore
    from llama_index.embeddings.huggingface import HuggingFaceEmbedding
    from llama_index import VectorStoreIndex, Document
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False
    print("ChromaDB not available, using simple storage")


class KOLKnowledgeBase:
    """KOL 知识库类"""
    
    def __init__(self, persist_dir: str = "data/knowledge"):
        """初始化知识库"""
        self.persist_dir = persist_dir
        os.makedirs(persist_dir, exist_ok=True)
        
        if CHROMADB_AVAILABLE:
            self._setup_vector_store()
        else:
            self._setup_simple_store()
    
    def _setup_vector_store(self):
        """设置向量存储"""
        try:
            # 初始化ChromaDB
            self.chroma_client = chromadb.PersistentClient(path=self.persist_dir)
            self.collection = self.chroma_client.get_or_create_collection("kol_knowledge")
            
            # 初始化向量存储
            self.vector_store = ChromaVectorStore(chroma_collection=self.collection)
            
            # 初始化嵌入模型
            self.embed_model = HuggingFaceEmbedding(
                model_name="sentence-transformers/all-MiniLM-L6-v2"
            )
            
            # 创建索引
            self.index = VectorStoreIndex.from_vector_store(
                self.vector_store,
                embed_model=self.embed_model
            )
            
            self.use_vector_store = True
            print("✅ 向量存储初始化成功")
            
        except Exception as e:
            print(f"向量存储初始化失败: {e}")
            self._setup_simple_store()
    
    def _setup_simple_store(self):
        """设置简单存储（备用方案）"""
        self.knowledge_file = os.path.join(self.persist_dir, "kol_knowledge.json")
        self.knowledge_data = self._load_simple_knowledge()
        self.use_vector_store = False
        print("✅ 简单存储初始化成功")
    
    def _load_simple_knowledge(self) -> Dict:
        """加载简单知识库数据"""
        if os.path.exists(self.knowledge_file):
            try:
                with open(self.knowledge_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载知识库失败: {e}")
        
        return {
            "kol_profiles": {},
            "coin_analysis": {},
            "market_events": [],
            "analysis_history": []
        }
    
    def _save_simple_knowledge(self):
        """保存简单知识库数据"""
        try:
            with open(self.knowledge_file, 'w', encoding='utf-8') as f:
                json.dump(self.knowledge_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存知识库失败: {e}")
    
    def add_kol_profile(self, username: str, profile_data: Dict):
        """添加KOL档案"""
        if self.use_vector_store:
            self._add_to_vector_store(f"kol_profile_{username}", profile_data)
        else:
            self.knowledge_data["kol_profiles"][username] = {
                **profile_data,
                "updated_at": datetime.now().isoformat()
            }
            self._save_simple_knowledge()
    
    def add_analysis_result(self, coin_symbol: str, analysis_data: Dict):
        """添加分析结果"""
        if self.use_vector_store:
            self._add_to_vector_store(f"analysis_{coin_symbol}", analysis_data)
        else:
            if coin_symbol not in self.knowledge_data["coin_analysis"]:
                self.knowledge_data["coin_analysis"][coin_symbol] = []
            
            self.knowledge_data["coin_analysis"][coin_symbol].append({
                **analysis_data,
                "timestamp": datetime.now().isoformat()
            })
            self._save_simple_knowledge()
    
    def add_market_event(self, event_data: Dict):
        """添加市场事件"""
        if self.use_vector_store:
            self._add_to_vector_store(f"market_event_{datetime.now().timestamp()}", event_data)
        else:
            self.knowledge_data["market_events"].append({
                **event_data,
                "timestamp": datetime.now().isoformat()
            })
            self._save_simple_knowledge()
    
    def _add_to_vector_store(self, doc_id: str, data: Dict):
        """添加到向量存储"""
        try:
            # 创建文档
            text_content = json.dumps(data, ensure_ascii=False)
            document = Document(text=text_content, doc_id=doc_id, metadata=data)
            
            # 添加到索引
            self.index.insert(document)
            
        except Exception as e:
            print(f"向量存储添加失败: {e}")
    
    def search_kol_info(self, username: str) -> Optional[Dict]:
        """搜索KOL信息"""
        if self.use_vector_store:
            return self._search_vector_store(f"kol_profile_{username}")
        else:
            return self.knowledge_data["kol_profiles"].get(username)
    
    def search_coin_analysis(self, coin_symbol: str, limit: int = 5) -> List[Dict]:
        """搜索币种分析历史"""
        if self.use_vector_store:
            results = self._search_vector_store(f"analysis_{coin_symbol}", limit)
            return results if isinstance(results, list) else [results] if results else []
        else:
            analyses = self.knowledge_data["coin_analysis"].get(coin_symbol, [])
            return sorted(analyses, key=lambda x: x.get("timestamp", ""), reverse=True)[:limit]
    
    def search_market_events(self, query: str, limit: int = 10) -> List[Dict]:
        """搜索市场事件"""
        if self.use_vector_store:
            return self._search_vector_store_by_query(query, limit)
        else:
            # 简单的关键词匹配
            events = []
            query_lower = query.lower()
            
            for event in self.knowledge_data["market_events"]:
                event_text = json.dumps(event, ensure_ascii=False).lower()
                if query_lower in event_text:
                    events.append(event)
            
            return sorted(events, key=lambda x: x.get("timestamp", ""), reverse=True)[:limit]
    
    def _search_vector_store(self, doc_id: str, limit: int = 1):
        """在向量存储中搜索"""
        try:
            query_engine = self.index.as_query_engine(similarity_top_k=limit)
            response = query_engine.query(doc_id)
            return response.metadata if hasattr(response, 'metadata') else None
        except Exception as e:
            print(f"向量搜索失败: {e}")
            return None
    
    def _search_vector_store_by_query(self, query: str, limit: int = 5):
        """通过查询在向量存储中搜索"""
        try:
            query_engine = self.index.as_query_engine(similarity_top_k=limit)
            response = query_engine.query(query)
            
            results = []
            if hasattr(response, 'source_nodes'):
                for node in response.source_nodes:
                    if hasattr(node, 'metadata'):
                        results.append(node.metadata)
            
            return results
        except Exception as e:
            print(f"向量查询搜索失败: {e}")
            return []
    
    def get_knowledge_stats(self) -> Dict:
        """获取知识库统计信息"""
        if self.use_vector_store:
            try:
                count = self.collection.count()
                return {
                    "total_documents": count,
                    "storage_type": "vector_store",
                    "embedding_model": "sentence-transformers/all-MiniLM-L6-v2"
                }
            except:
                return {"storage_type": "vector_store", "status": "error"}
        else:
            return {
                "kol_profiles": len(self.knowledge_data["kol_profiles"]),
                "coin_analyses": sum(len(analyses) for analyses in self.knowledge_data["coin_analysis"].values()),
                "market_events": len(self.knowledge_data["market_events"]),
                "storage_type": "simple_store"
            }
