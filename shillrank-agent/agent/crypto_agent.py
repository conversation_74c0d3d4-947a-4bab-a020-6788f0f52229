"""
CryptoKOL Agent - 智能加密货币KOL分析助手
"""

from typing import List, Dict, Any, Optional
from llama_index.agent import ReActAgent
from llama_index.llms.huggingface import HuggingFaceLLM
from llama_index.embeddings.huggingface import HuggingFaceEmbedding
from llama_index.tools import BaseTool
from llama_index.memory import ChatMemoryBuffer

from .tools.coingecko_tool import CoinGeckoTool
from .tools.twitter_tool import TwitterAnalysisTool
from .tools.analysis_tool import KOLAnalysisTool

import os
import json


class CryptoKOLAgent:
    """加密货币KOL分析智能助手"""
    
    def __init__(self, hf_token: Optional[str] = None):
        """初始化Agent"""
        self.hf_token = hf_token or os.getenv("HUGGINGFACE_API_TOKEN", "")
        
        # 初始化LLM
        self.llm = self._setup_llm()
        
        # 初始化工具
        self.tools = self._setup_tools()
        
        # 初始化Agent
        self.agent = self._setup_agent()
        
        # 系统提示词
        self.system_prompt = self._get_system_prompt()
    
    def _setup_llm(self) -> HuggingFaceLLM:
        """设置语言模型"""
        try:
            # 使用较小的开源模型，适合Hugging Face Space
            llm = HuggingFaceLLM(
                model_name="microsoft/DialoGPT-medium",
                tokenizer_name="microsoft/DialoGPT-medium",
                context_window=2048,
                max_new_tokens=512,
                generate_kwargs={"temperature": 0.7, "do_sample": True},
                system_prompt=self._get_system_prompt(),
                query_wrapper_prompt="User: {query_str}\nAssistant: ",
                tokenizer_kwargs={"max_length": 2048},
                model_kwargs={"torch_dtype": "auto"},
                device_map="auto",
            )
            return llm
        except Exception as e:
            print(f"LLM初始化失败，使用备用配置: {e}")
            # 备用：使用API方式
            return self._setup_api_llm()
    
    def _setup_api_llm(self):
        """设置API方式的LLM（备用方案）"""
        from llama_index.llms.huggingface import HuggingFaceInferenceAPI
        
        if self.hf_token:
            return HuggingFaceInferenceAPI(
                model_name="microsoft/DialoGPT-medium",
                token=self.hf_token,
                context_window=2048,
                max_new_tokens=512,
            )
        else:
            # 最简单的备用方案
            return None
    
    def _setup_tools(self) -> List[BaseTool]:
        """设置Agent工具"""
        tools = [
            CoinGeckoTool(),
            TwitterAnalysisTool(),
            KOLAnalysisTool()
        ]
        return tools
    
    def _setup_agent(self) -> ReActAgent:
        """设置ReAct Agent"""
        try:
            # 创建内存缓冲区
            memory = ChatMemoryBuffer.from_defaults(token_limit=1500)
            
            # 创建Agent
            agent = ReActAgent.from_tools(
                tools=self.tools,
                llm=self.llm,
                memory=memory,
                verbose=True,
                max_iterations=5,
                system_prompt=self.system_prompt
            )
            
            return agent
        except Exception as e:
            print(f"Agent初始化失败: {e}")
            return None
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """
你是CryptoKOL Agent，一个专业的加密货币KOL影响力分析助手。

你的主要能力包括：
1. 🔍 搜索和分析加密货币信息
2. 📊 分析KOL（意见领袖）对币种的影响力
3. 💹 计算价格影响和市场数据
4. 🐦 分析Twitter推文和社交媒体数据
5. 📈 生成综合排名和报告

可用工具：
- coingecko_tool: 获取币种信息、价格数据
- twitter_analysis_tool: 分析KOL推文和情绪
- kol_analysis_tool: 综合分析和排名

回答规则：
1. 使用中文回答
2. 提供准确的数据分析
3. 给出实用的投资参考（但声明不构成投资建议）
4. 保持专业和客观
5. 如果数据不足，诚实说明限制

示例对话：
用户："帮我分析一下PEPE币最近的KOL影响力"
你应该：
1. 使用coingecko_tool搜索PEPE币信息
2. 使用kol_analysis_tool分析KOL影响力
3. 总结分析结果并给出见解

开始对话时，简单介绍你的能力并询问用户需要什么帮助。
"""
    
    def chat(self, message: str) -> str:
        """与Agent对话"""
        try:
            if not self.agent:
                return self._fallback_response(message)
            
            response = self.agent.chat(message)
            return str(response)
        
        except Exception as e:
            print(f"Agent对话失败: {e}")
            return self._fallback_response(message)
    
    def _fallback_response(self, message: str) -> str:
        """备用响应机制"""
        # 简单的关键词匹配响应
        message_lower = message.lower()
        
        if any(word in message_lower for word in ['分析', 'analyze', '币', 'coin']):
            return """
我是CryptoKOL Agent，专门分析加密货币KOL影响力。

我可以帮你：
🔍 搜索币种信息和价格
📊 分析KOL对特定币种的影响力
💹 计算价格影响和趋势
📈 生成排行榜和报告

请告诉我你想分析哪个币种？比如：
- "分析PEPE币的KOL影响力"
- "查看DOGE的价格和KOL数据"
- "比较几个KOL对BTC的影响"

注意：分析结果仅供参考，不构成投资建议。
"""
        
        elif any(word in message_lower for word in ['你好', 'hello', 'hi']):
            return """
你好！我是CryptoKOL Agent 🤖

我专门分析加密货币KOL（意见领袖）的影响力，可以帮你：

📊 **核心功能**
• 分析KOL对特定币种的影响力排名
• 计算推文对价格的影响
• 提供市场数据和趋势分析
• 生成详细的分析报告

💡 **使用示例**
• "分析PEPE币最近7天的KOL影响力"
• "查看Elon Musk对DOGE的影响"
• "比较几个顶级KOL的表现"

请告诉我你想了解什么？
"""
        
        else:
            return """
抱歉，我现在处于简化模式。

我是CryptoKOL Agent，专门分析加密货币KOL影响力。

请尝试这样问我：
• "分析[币种名称]的KOL影响力"
• "查看[币种]的价格数据"
• "帮我分析PEPE币"

我会尽力为你提供专业的分析！
"""
    
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return [tool.name for tool in self.tools]
    
    def reset_conversation(self):
        """重置对话历史"""
        if self.agent and hasattr(self.agent, 'memory'):
            self.agent.memory.reset()
