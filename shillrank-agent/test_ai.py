#!/usr/bin/env python3
"""
Test script for ShillRank AI Engine
Tests both with and without Hugging Face API token
"""

import os
from ai_engine import <PERSON>llRankAI

def test_ai_engine():
    print("🚀 Testing ShillRank AI Engine")
    print("=" * 50)
    
    # Check HF_TOKEN
    hf_token = os.environ.get("HF_TOKEN")
    if hf_token and hf_token != "your_token_here":
        print(f"🔑 HF_TOKEN: Found (length: {len(hf_token)})")
        print("🤖 Will test with real Hugging Face API")
    else:
        print("⚠️ HF_TOKEN: Not found or placeholder")
        print("🤖 Will test with fallback mode")
    
    print("\n" + "=" * 50)
    
    # Initialize AI engine
    print("🔧 Initializing AI Engine...")
    ai = ShillRankAI()
    
    print("\n" + "=" * 50)
    
    # Test KOL analysis
    print("📊 Testing KOL Analysis...")
    test_kols = ["CryptoWhale", "ElonMusk", "DegenTrader"]
    
    for kol in test_kols:
        print(f"\n🎯 Analyzing @{kol}...")
        try:
            result = ai.analyze_kol_performance(kol)
            metrics = result["metrics"]
            
            print(f"  ✅ Followers: {metrics['followers']:,}")
            print(f"  ✅ Accuracy: {metrics['accuracy']:.1f}%")
            print(f"  ✅ Avg Gain: +{metrics['average_gain']:.0f}%")
            print(f"  ✅ Specialty: {metrics['specialty']}")
            
            # Show AI analysis if available
            ai_analysis = result.get("ai_analysis", "")
            if ai_analysis and "unavailable" not in ai_analysis.lower():
                print(f"  🧠 AI Analysis: {ai_analysis[:100]}...")
            else:
                print(f"  ⚠️ AI Analysis: {ai_analysis}")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")
    
    print("\n" + "=" * 50)
    
    # Test similar KOLs
    print("🔍 Testing Similar KOL Matching...")
    try:
        target_kol = "CryptoWhale"
        analysis = ai.analyze_kol_performance(target_kol)
        similar_kols = ai.find_similar_kols(target_kol, analysis)
        
        print(f"✅ Found {len(similar_kols)} similar KOLs to @{target_kol}:")
        for kol in similar_kols:
            print(f"  - @{kol['username']}: {kol['similarity']}% similarity")
            
    except Exception as e:
        print(f"❌ Similar KOL test failed: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 AI Engine test completed!")
    
    # Show setup instructions if no token
    if not hf_token or hf_token == "your_token_here":
        print("\n💡 To enable real AI analysis:")
        print("1. Get a Hugging Face token: https://huggingface.co/settings/tokens")
        print("2. Set environment variable: export HF_TOKEN=your_token")
        print("3. Or create .env file with HF_TOKEN=your_token")

if __name__ == "__main__":
    test_ai_engine()
