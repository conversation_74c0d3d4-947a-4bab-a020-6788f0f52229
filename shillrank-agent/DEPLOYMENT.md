
# 🚀 Hugging Face Space 部署指南

## 📋 部署步骤

### 1. 创建 Hugging Face Space
1. 访问 https://huggingface.co/spaces
2. 点击 "Create new Space"
3. 填写信息：
   - Space name: `shillrank-agent`
   - License: `MIT`
   - SDK: `Gradio`
   - Hardware: `CPU basic` (免费)

### 2. 上传文件
将 `space/` 目录下的所有文件上传到你的 Space 仓库：

```bash
git clone https://huggingface.co/spaces/YOUR_USERNAME/shillrank-agent
cd shillrank-agent
cp -r ../space/* .
git add .
git commit -m "Initial commit: ShillRank Agent"
git push
```

### 3. 配置环境变量（可选）
在 Space 设置中添加：
- `HUGGINGFACE_API_TOKEN`: 你的 HF API Token

### 4. 等待构建
Space 会自动构建和部署，通常需要 5-10 分钟。

## 🔧 故障排除

### 常见问题
1. **依赖安装失败**: 检查 requirements.txt 中的包版本
2. **内存不足**: 考虑升级到付费硬件
3. **API 限制**: 确保 API Token 有效

### 优化建议
1. 使用缓存减少 API 调用
2. 限制并发用户数
3. 优化模型加载

## 📊 监控
- 查看 Space 日志了解运行状态
- 监控 API 使用量
- 收集用户反馈

## 🎯 竞赛提交
确保 Space 包含：
- ✅ 完整的功能演示
- ✅ 清晰的使用说明
- ✅ 技术架构说明
- ✅ 开源代码
