"""
ShillRank 配置文件
"""
import os
from dotenv import load_dotenv

load_dotenv()

# Hugging Face 配置
HUGGINGFACE_API_TOKEN = os.getenv("HUGGINGFACE_API_TOKEN", "")
HUGGINGFACE_MODEL = "microsoft/DialoGPT-medium"  # 可以换成其他模型

# 数据配置
CACHE_DIR = "data/cache"
KOL_LIST_FILE = "data/kol_list.json"

# 分析参数
DEFAULT_DAYS = 7
MAX_DAYS = 30
MIN_TWEET_INTERACTIONS = 10  # 最小互动数阈值

# 评分权重
SCORE_WEIGHTS = {
    "price_impact": 0.4,    # 价格影响权重
    "interactions": 0.3,    # 互动数权重
    "frequency": 0.2,       # 提及频次权重
    "sentiment": 0.1        # 情绪权重
}

# CoinGecko API 配置
COINGECKO_API_URL = "https://api.coingecko.com/api/v3"
RATE_LIMIT_DELAY = 1.2  # API 调用间隔（秒）

# 推文抓取配置
MAX_TWEETS_PER_KOL = 100
TWEET_SEARCH_KEYWORDS = ["$", "crypto", "coin", "token"]

# UI 配置
APP_TITLE = "💥 ShillRank - 币圈 KOL 影响力排行榜"
APP_DESCRIPTION = """
🧠 基于推文和行情数据，量化分析币圈 KOL 喊单影响力

输入币种名称（如 $PEPE），选择分析时间范围，获取 KOL 影响力排行榜！
"""

# Agent 配置
AGENT_ENABLED = True
AGENT_MODEL = "microsoft/DialoGPT-medium"
AGENT_MAX_ITERATIONS = 5
AGENT_CONTEXT_WINDOW = 2048
AGENT_MAX_NEW_TOKENS = 512
AGENT_TEMPERATURE = 0.7

# 知识库配置
KNOWLEDGE_BASE_DIR = "data/knowledge"
ENABLE_VECTOR_STORE = True
EMBEDDING_MODEL = "sentence-transformers/all-MiniLM-L6-v2"
