"""
ShillRank Agent - Professional UI Version
Modern design with professional styling
"""

import gradio as gr

# Professional CSS styling
PROFESSIONAL_CSS = """
/* Import professional fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Global styles */
* {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Main container */
.gradio-container {
    max-width: 1400px !important;
    margin: 0 auto !important;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%) !important;
    min-height: 100vh;
}

/* Header styling */
.main-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    text-align: center;
}

.main-header h1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.main-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
    margin: 0.5rem 0;
}

/* Input styling */
.input-container {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

/* Button styling */
.primary-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    padding: 12px 24px;
    transition: all 0.3s ease;
}

.primary-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.secondary-button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    padding: 12px 24px;
    transition: all 0.3s ease;
}

/* Output container */
.output-container {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    min-height: 400px;
}

/* Sidebar styling */
.sidebar {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
}

.sidebar h3 {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    margin-bottom: 1rem;
}

.sidebar ul {
    list-style: none;
    padding: 0;
}

.sidebar li {
    color: rgba(255, 255, 255, 0.7);
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Examples styling */
.examples-container {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 1rem;
}

/* Footer styling */
.footer {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 2rem;
    margin-top: 2rem;
    text-align: center;
}

.footer p {
    color: rgba(255, 255, 255, 0.6);
    margin: 0.5rem 0;
}

/* Responsive design */
@media (max-width: 768px) {
    .main-header h1 {
        font-size: 2rem;
    }
    
    .gradio-container {
        padding: 1rem;
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

/* Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeInUp 0.6s ease-out;
}
"""

def create_professional_header():
    """创建专业的页面头部"""
    return """
    <div class="main-header animate-fade-in">
        <h1>🚀 ShillRank Agent</h1>
        <p><strong>Professional Cryptocurrency KOL Influence Analysis Platform</strong></p>
        <p>Advanced AI-powered analytics for crypto market intelligence</p>
        <div style="display: flex; justify-content: center; gap: 1rem; margin-top: 1rem; flex-wrap: wrap;">
            <span style="background: rgba(102, 126, 234, 0.2); color: #667eea; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">
                📊 Real-time Analysis
            </span>
            <span style="background: rgba(118, 75, 162, 0.2); color: #764ba2; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">
                🎯 KOL Attribution
            </span>
            <span style="background: rgba(0, 255, 136, 0.2); color: #00ff88; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">
                💎 DEX Integration
            </span>
        </div>
    </div>
    """

def create_professional_sidebar():
    """创建专业的侧边栏"""
    return """
    <div class="sidebar animate-fade-in">
        <h3>🎯 Core Features</h3>
        <ul>
            <li>📊 KOL Performance Tables</li>
            <li>📈 Contribution Analysis</li>
            <li>🔍 Token Comparison</li>
            <li>💹 Real-time Tracking</li>
            <li>🎨 Visual Analytics</li>
        </ul>
        
        <h3>💎 Supported Assets</h3>
        <ul>
            <li>🪙 Major Cryptocurrencies</li>
            <li>🐸 Meme Coins (PEPE, DOGE)</li>
            <li>🚀 DEX Tokens (WOJAK, TURBO)</li>
            <li>⚡ Layer 1 Tokens</li>
            <li>🔗 DeFi Protocols</li>
        </ul>
        
        <h3>🔬 Analytics</h3>
        <ul>
            <li>📊 Attribution Modeling</li>
            <li>🎯 Influence Scoring</li>
            <li>📈 Performance Tracking</li>
            <li>🔍 Sentiment Analysis</li>
            <li>💡 Predictive Insights</li>
        </ul>
    </div>
    """

def create_professional_footer():
    """创建专业的页脚"""
    return """
    <div class="footer animate-fade-in">
        <p><strong>🏆 Agent Demo Marathon - Track 3 Submission</strong></p>
        <p>🤖 Powered by Advanced AI • 📊 Real-time Data • 🔒 Professional Grade</p>
        <p>⚠️ <em>For educational and research purposes only. Not financial advice.</em></p>
        <div style="margin-top: 1rem; display: flex; justify-content: center; gap: 2rem; flex-wrap: wrap;">
            <span style="color: rgba(255, 255, 255, 0.5);">🔧 Built with Gradio</span>
            <span style="color: rgba(255, 255, 255, 0.5);">🎨 Modern UI/UX</span>
            <span style="color: rgba(255, 255, 255, 0.5);">⚡ Real-time Analytics</span>
        </div>
    </div>
    """

# Import the main agent function
try:
    from app import shillrank_agent_with_charts
except ImportError:
    # Fallback if import fails
    def shillrank_agent_with_charts(message):
        return f"<div>Processing: {message}</div>"

def shillrank_agent_professional(message):
    """Professional wrapper that integrates all agent functionality"""
    if not message or not message.strip():
        return """
        <div style="background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 2rem; text-align: center;">
            <h3 style="color: rgba(255, 255, 255, 0.9); margin-bottom: 1rem;">👋 Welcome to ShillRank Agent</h3>
            <p style="color: rgba(255, 255, 255, 0.7); margin-bottom: 1.5rem;">
                Your professional cryptocurrency KOL influence analysis platform
            </p>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1.5rem;">
                <div style="background: rgba(102, 126, 234, 0.1); border: 1px solid rgba(102, 126, 234, 0.3); border-radius: 8px; padding: 1rem;">
                    <h4 style="color: #667eea; margin: 0 0 0.5rem 0;">📊 Performance Analysis</h4>
                    <p style="color: rgba(255, 255, 255, 0.6); font-size: 0.9rem; margin: 0;">Track KOL performance with professional data tables</p>
                </div>
                <div style="background: rgba(118, 75, 162, 0.1); border: 1px solid rgba(118, 75, 162, 0.3); border-radius: 8px; padding: 1rem;">
                    <h4 style="color: #764ba2; margin: 0 0 0.5rem 0;">🎯 Attribution Analysis</h4>
                    <p style="color: rgba(255, 255, 255, 0.6); font-size: 0.9rem; margin: 0;">Quantify KOL contributions to price movements</p>
                </div>
                <div style="background: rgba(0, 255, 136, 0.1); border: 1px solid rgba(0, 255, 136, 0.3); border-radius: 8px; padding: 1rem;">
                    <h4 style="color: #00ff88; margin: 0 0 0.5rem 0;">💎 DEX Integration</h4>
                    <p style="color: rgba(255, 255, 255, 0.6); font-size: 0.9rem; margin: 0;">Analyze meme coins and DEX tokens</p>
                </div>
            </div>
        </div>
        """

    # Call the main agent function and wrap the result in professional styling
    try:
        result = shillrank_agent_with_charts(message)

        # Wrap the result in professional container
        professional_result = f"""
        <div style="background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 0; overflow: hidden;">
            {result}
        </div>
        """

        return professional_result

    except Exception as e:
        # Fallback error handling with professional styling
        return f"""
        <div style="background: rgba(255, 87, 87, 0.1); border: 1px solid rgba(255, 87, 87, 0.3); border-radius: 12px; padding: 1.5rem;">
            <h4 style="color: #ff5757; margin-bottom: 1rem;">⚠️ Analysis Error</h4>
            <p style="color: rgba(255, 255, 255, 0.8);">
                Unable to process query: <strong>"{message}"</strong>
            </p>
            <p style="color: rgba(255, 255, 255, 0.6);">
                Please try a different query or contact support.
            </p>
        </div>
        """

# Create the professional interface
with gr.Blocks(
    title="ShillRank Agent - Professional",
    theme=gr.themes.Base(),
    css=PROFESSIONAL_CSS
) as demo:
    
    # Header
    gr.HTML(create_professional_header())
    
    # Main content area
    with gr.Row():
        # Main analysis area
        with gr.Column(scale=3):
            # Input section
            with gr.Group():
                gr.HTML('<div class="input-container">')
                msg_input = gr.Textbox(
                    label="🔍 Enter your analysis query",
                    placeholder="Try: '@CryptoWhale performance table' or 'PEPE contribution analysis'",
                    lines=2,
                    elem_classes=["professional-input"]
                )
                
                with gr.Row():
                    send_btn = gr.Button("🚀 Analyze", variant="primary", elem_classes=["primary-button"])
                    clear_btn = gr.Button("🗑️ Clear", variant="secondary", elem_classes=["secondary-button"])
                gr.HTML('</div>')
            
            # Output section
            with gr.Group():
                gr.HTML('<div class="output-container">')
                output = gr.HTML(
                    label="📊 Analysis Results",
                    elem_classes=["professional-output"]
                )
                gr.HTML('</div>')
        
        # Sidebar
        with gr.Column(scale=1):
            gr.HTML(create_professional_sidebar())
    
    # Examples section
    with gr.Group():
        gr.HTML('<div class="examples-container">')
        gr.Examples(
            examples=[
                "@CryptoWhale performance table",
                "WOJAK contribution analysis",
                "TURBO pump attribution",
                "@ElonMusk mentioned coins",
                "Show professional charts",
                "PEPE KOL impact analysis",
                "DEX meme coin analysis"
            ],
            inputs=msg_input,
            label="💡 Example Queries"
        )
        gr.HTML('</div>')
    
    # Footer
    gr.HTML(create_professional_footer())
    
    # Event handlers
    send_btn.click(
        fn=shillrank_agent_professional,
        inputs=[msg_input],
        outputs=[output]
    )
    
    msg_input.submit(
        fn=shillrank_agent_professional,
        inputs=[msg_input],
        outputs=[output]
    )
    
    clear_btn.click(
        fn=lambda: ("", ""),
        outputs=[msg_input, output]
    )

if __name__ == "__main__":
    demo.launch()
