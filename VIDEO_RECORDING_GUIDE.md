# 🎥 ShillRank Agent 视频录制指南

## 📋 竞赛要求
Track 3需要在README.md中包含视频概述链接，解释应用的使用方法和目的。

## 🎯 视频内容建议

### 视频时长：3-5分钟

### 📝 脚本大纲：

#### 1. 开场介绍 (30秒)
```
"大家好，我是[你的名字]，这是我为Agent Demo马拉松创建的ShillRank Agent。

这是一个智能的加密货币KOL影响力分析助手，通过对话式AI帮助用户分析加密货币意见领袖的市场影响力。"
```

#### 2. 问题背景 (30秒)
```
"加密货币投资者面临的挑战：
- 社交媒体信息过载
- 难以评估KOL的真实影响力
- 缺乏量化分析工具
- 需要专业技能才能进行深度分析"
```

#### 3. 解决方案演示 (2-3分钟)
```
"ShillRank Agent通过对话式AI解决这些问题：

[演示1] 基础交互
- 输入："你好，请介绍一下你的功能"
- 展示Agent的友好回应和功能介绍

[演示2] KOL影响力分析  
- 输入："分析PEPE币最近7天的KOL影响力"
- 展示：
  * 自动币种识别
  * 实时数据获取
  * 影响力排行榜生成
  * 可视化图表展示
  * AI分析总结

[演示3] 价格查询
- 输入："查看DOGE币的当前价格"
- 展示实时价格数据和市场信息

[演示4] 智能对话
- 展示多轮对话能力
- 上下文理解
- 个性化建议
```

#### 4. 技术亮点 (1分钟)
```
"技术创新点：
- 自然语言处理：理解用户意图
- 实时数据集成：CoinGecko + 社交媒体API
- 智能可视化：自动生成图表和表格
- 多维度分析：价格影响+互动数据+情绪分析
- 对话式体验：降低使用门槛"
```

#### 5. 结尾总结 (30秒)
```
"ShillRank Agent将传统的数据分析工具转换为智能对话助手，
让任何人都能轻松获得专业级的加密货币KOL影响力分析。

感谢观看，欢迎试用我们的Agent！"
```

## 🎬 录制技巧

### 准备工作：
1. **测试应用**：确保所有功能正常工作
2. **准备示例**：预先准备好演示用的查询
3. **清理界面**：确保屏幕整洁，字体大小合适
4. **网络检查**：确保网络稳定，API响应正常

### 录制设置：
- **分辨率**：1920x1080 或更高
- **帧率**：30fps
- **音频**：清晰的麦克风录音
- **屏幕**：全屏录制或聚焦应用窗口

### 录制流程：
1. **开场**：简短自我介绍
2. **演示**：按脚本进行功能演示
3. **互动**：展示真实的用户交互
4. **总结**：强调创新点和价值

## 📱 录制工具推荐

### 免费工具：
- **OBS Studio** (跨平台)
- **QuickTime Player** (Mac)
- **Windows Game Bar** (Windows)
- **Loom** (在线录制)

### 付费工具：
- **Camtasia**
- **ScreenFlow** (Mac)
- **Bandicam** (Windows)

## 🎞️ 后期处理

### 基础编辑：
- 剪切多余部分
- 添加字幕（可选）
- 调整音量
- 添加开场/结尾标题

### 优化建议：
- 保持节奏紧凑
- 突出关键功能
- 确保音画同步
- 导出高质量视频

## 📤 上传和分享

### 推荐平台：
1. **YouTube** (推荐)
   - 创建公开视频
   - 添加描述和标签
   - 获取分享链接

2. **Vimeo**
   - 专业视频平台
   - 更好的播放质量

3. **Bilibili** (中文用户)
   - 适合中文内容
   - 技术社区活跃

### 视频信息：
- **标题**：ShillRank Agent - Conversational AI for Crypto KOL Analysis
- **描述**：包含项目介绍、技术栈、GitHub链接
- **标签**：AI, Agent, Cryptocurrency, KOL, Analysis, Gradio, Hugging Face

## 🔗 更新README

录制完成后，将视频链接更新到README.md：

```markdown
## 🎥 Video Overview

**[📹 Watch Demo Video - ShillRank Agent Overview](https://youtu.be/YOUR_ACTUAL_VIDEO_ID)**

*This video demonstrates the key features and usage of ShillRank Agent, including conversational AI interaction, real-time KOL analysis, and intelligent visualization capabilities.*
```

## ✅ 检查清单

录制前确认：
- [ ] 应用功能正常
- [ ] 网络连接稳定
- [ ] 录制设备就绪
- [ ] 演示脚本准备
- [ ] 示例查询测试

录制后确认：
- [ ] 视频质量清晰
- [ ] 音频清楚
- [ ] 功能演示完整
- [ ] 时长适中(3-5分钟)
- [ ] 上传到平台
- [ ] 更新README链接

## 🏆 成功提交

完成视频录制和上传后，你的Agent Demo马拉松提交将包含：

✅ 完整的Gradio应用  
✅ 发布到Hugging Face Spaces  
✅ README.md包含"agent-demo-track"标签  
✅ README.md包含视频概述链接  

祝你录制顺利！🎬
