# 🤖 ShillRank Agent - Crypto KOL Influence Analysis AI Assistant

**An intelligent conversational AI agent for analyzing cryptocurrency Key Opinion Leader (KOL) influence and market impact.**

[![Hugging Face Space](https://img.shields.io/badge/🤗-Hugging%20Face%20Space-blue)](https://huggingface.co/spaces/YOUR_USERNAME/shillrank-agent)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)

## 🎯 Project Overview

ShillRank Agent transforms traditional cryptocurrency analysis into an intelligent conversational experience. Users can interact with the AI agent using natural language to get comprehensive KOL influence analysis, real-time price data, and market insights.

### 🚀 Key Features

- **🤖 Conversational AI Interface**: Natural language interaction powered by advanced NLP
- **📊 KOL Influence Analysis**: Comprehensive ranking system for crypto influencers
- **💹 Real-time Price Tracking**: Live cryptocurrency market data integration
- **🐦 Social Media Analytics**: Twitter sentiment analysis and engagement metrics
- **📈 Intelligent Visualization**: Auto-generated charts and interactive dashboards
- **🔍 Multi-dimensional Analysis**: Price impact, interaction rates, mention frequency, and sentiment scoring

## 🏗️ Architecture

```
┌─────────────────────────────────────────┐
│           Gradio ChatInterface          │
│         (User Interaction Layer)        │
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│            Hybrid AI Agent             │
│  ┌─────────────┐  ┌─────────────────┐  │
│  │   Natural   │  │   Intent        │  │
│  │  Language   │  │ Recognition     │  │
│  │ Processing  │  │   & Response    │  │
│  └─────────────┘  └─────────────────┘  │
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│            Analysis Engine              │
│  ┌─────────────┐ ┌─────────────────────┐│
│  │ CoinGecko   │ │   Twitter/Social    ││
│  │ Price API   │ │   Media Scraper     ││
│  └─────────────┘ └─────────────────────┘│
│  ┌─────────────────────────────────────┐ │
│  │        KOL Ranking Algorithm        │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│         Visualization Layer             │
│  ┌─────────────┐ ┌─────────────────────┐│
│  │   Plotly    │ │    Data Tables      ││
│  │   Charts    │ │   & Statistics      ││
│  └─────────────┘ └─────────────────────┘│
└─────────────────────────────────────────┘
```

## 💡 How It Works

### 1. **Natural Language Interaction**
Users can ask questions in plain English:
- "Analyze PEPE coin's KOL influence over the last 7 days"
- "Show me DOGE's current price and market data"
- "Compare the top crypto influencers for BTC"

### 2. **Intelligent Analysis**
The agent automatically:
- Identifies the cryptocurrency mentioned
- Extracts time parameters
- Performs comprehensive KOL analysis
- Generates visualizations

### 3. **Multi-modal Response**
Returns results in multiple formats:
- **Conversational text**: Easy-to-understand explanations
- **Interactive charts**: Visual ranking and trend analysis
- **Data tables**: Detailed metrics and statistics

## 🎮 Usage Examples

### Basic Interaction
```
User: "Hello, what can you do?"
Agent: [Introduces capabilities and available features]
```

### KOL Influence Analysis
```
User: "Analyze PEPE coin's KOL influence in the last 7 days"
Agent: 
1. Searches for PEPE coin information
2. Analyzes related KOL tweets and engagement
3. Calculates influence rankings
4. Generates comprehensive report with visualizations
```

### Price Data Queries
```
User: "What's the current price of DOGE?"
Agent: [Returns real-time price, market cap, 24h change, and trading volume]
```

### Comparative Analysis
```
User: "Compare top crypto influencers for ETH"
Agent: [Analyzes multiple KOLs and provides comparative rankings]
```

## 🔧 Technical Stack

- **Frontend**: Gradio ChatInterface
- **Backend**: Python with FastAPI-style architecture
- **AI/ML**: Hugging Face Transformers, Natural Language Processing
- **Data Sources**: 
  - CoinGecko API (cryptocurrency data)
  - Twitter/Social Media APIs (KOL content)
- **Visualization**: Plotly, Pandas
- **Deployment**: Hugging Face Spaces
- **Caching**: DiskCache for performance optimization

## 📊 Analysis Methodology

### KOL Ranking Algorithm
The agent uses a weighted scoring system:

- **Price Impact (40%)**: Cryptocurrency price changes following KOL posts
- **Engagement Rate (30%)**: Average likes, retweets, and comments
- **Mention Frequency (20%)**: How often the KOL mentions specific cryptocurrencies
- **Sentiment Analysis (10%)**: AI-powered sentiment scoring of posts

### Data Processing Pipeline
1. **Data Collection**: Real-time scraping of social media and price data
2. **Content Analysis**: NLP processing for sentiment and topic extraction
3. **Impact Calculation**: Correlation analysis between posts and price movements
4. **Ranking Generation**: Weighted scoring and leaderboard creation
5. **Visualization**: Auto-generation of charts and tables

## 🚀 Quick Start

### Option 1: Hugging Face Space (Recommended)
1. Visit the [Hugging Face Space](https://huggingface.co/spaces/YOUR_USERNAME/shillrank-agent)
2. Start chatting with the agent immediately
3. Try example queries or ask your own questions

### Option 2: Local Development
```bash
# Clone the repository
git clone https://github.com/YOUR_USERNAME/ShillRank.git
cd ShillRank

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
export HUGGINGFACE_API_TOKEN="your_token_here"

# Run the application
python hybrid_agent_app.py
```

## 🎯 Agent Demo Marathon Highlights

### Innovation Points
- **Traditional → AI Transformation**: Converted a static analysis tool into an intelligent conversational agent
- **Hybrid Interface**: Combines natural language interaction with intelligent data visualization
- **Real-world Application**: Addresses genuine needs in the cryptocurrency investment community
- **Technical Excellence**: Demonstrates advanced NLP, data analysis, and visualization capabilities

### Competitive Advantages
1. **Practical Value**: Solves real cryptocurrency analysis challenges
2. **User Experience**: Intuitive natural language interface
3. **Technical Sophistication**: Multi-modal AI agent with advanced analytics
4. **Scalability**: Modular architecture allows easy feature expansion
5. **Community Impact**: Provides valuable tools for crypto investors and researchers

## 📈 Sample Analysis Output

```
✅ PEPE (PEPE) KOL Influence Analysis Complete!

📊 Basic Information
• Cryptocurrency: Pepe (PEPE)
• Analysis Period: 7 days
• Active KOLs: 12 influencers

💰 Current Price
• Price: $0.000001234
• 24h Change: +15.67%

🏆 Top 3 Influencers
1. CryptoWhale - Score: 87.3
2. PepeMaximalist - Score: 82.1
3. MemeKing - Score: 78.9

🤖 AI Analysis Summary
Recent PEPE mentions show strong bullish sentiment with significant 
price correlation. Top influencers demonstrate consistent engagement 
and market-moving potential.

💡 View More
• Right panel shows complete ranking chart
• Bottom table displays detailed metrics
• Ask about other cryptocurrencies for comparison
```

## ⚠️ Disclaimer

This tool is designed for research and analysis purposes only. The analysis results are for reference only and do not constitute investment advice. Cryptocurrency investments carry significant risks, and users should conduct their own research and consult with financial advisors before making investment decisions.

## 🤝 Contributing

We welcome contributions to improve ShillRank Agent! Please feel free to:
- Report bugs and issues
- Suggest new features
- Submit pull requests
- Improve documentation

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Hugging Face** for providing the deployment platform and AI models
- **CoinGecko** for cryptocurrency market data
- **Gradio** for the intuitive interface framework
- **Open Source Community** for the various libraries and tools used

---

**Built for the Agent Demo Marathon** 🏆  
**Transforming Crypto Analysis with Conversational AI**
