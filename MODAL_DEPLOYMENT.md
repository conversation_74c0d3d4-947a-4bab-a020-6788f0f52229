# 🚀 Modal 部署指南 (可选)

## 📋 Modal vs Hugging Face Space 对比

| 特性 | Hugging Face Space | Modal |
|------|-------------------|-------|
| 💰 费用 | 免费 (基础版) | 付费使用 |
| 🎯 适用场景 | AI应用展示、竞赛 | 生产环境、企业级 |
| 🔧 部署复杂度 | 简单 (拖拽上传) | 中等 (需要配置) |
| 📊 性能 | 基础CPU | 可配置高性能 |
| 🌐 访问性 | 公开展示友好 | 更适合内部使用 |

## 🎯 推荐选择

**对于Agent Demo马拉松，推荐使用 Hugging Face Space**，因为：
- ✅ 免费使用
- ✅ 更适合竞赛展示
- ✅ 社区友好
- ✅ 部署简单

## 🔧 如果选择Modal部署

### 1. 安装Modal
```bash
pip install modal
```

### 2. 设置Modal账户
```bash
modal setup
```

### 3. 创建Secret
在Modal控制台中创建名为 `huggingface-secret` 的Secret，包含：
```
HUGGINGFACE_API_TOKEN=*************************************
```

### 4. 部署应用
```bash
modal deploy modal_app.py
```

### 5. 运行应用
```bash
modal run modal_app.py
```

## 💡 建议

**继续使用Hugging Face Space部署**，因为它更适合你的需求和竞赛要求。

Modal更适合需要高性能、大规模部署的生产环境。
