"""
LLM 分析模块 - 使用 Hugging Face API
"""
import requests
import json
from typing import Dict, List, Optional
from config import HUGGINGFACE_API_TOKEN
import time

class LLMAnalyzer:
    def __init__(self):
        self.api_token = HUGGINGFACE_API_TOKEN
        self.headers = {"Authorization": f"Bearer {self.api_token}"}
        
        # 使用免费的情绪分析模型
        self.sentiment_model = "cardiffnlp/twitter-roberta-base-sentiment-latest"
        self.summarization_model = "facebook/bart-large-cnn"
        
    def analyze_sentiment(self, text: str) -> Dict[str, float]:
        """分析文本情绪"""
        if not self.api_token:
            # 如果没有 API token，使用简单的关键词分析
            return self._simple_sentiment_analysis(text)
            
        try:
            api_url = f"https://api-inference.huggingface.co/models/{self.sentiment_model}"
            
            payload = {"inputs": text}
            response = requests.post(api_url, headers=self.headers, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list) and len(result) > 0:
                    scores = {}
                    for item in result[0]:
                        label = item['label'].lower()
                        score = item['score']
                        scores[label] = score
                    return scores
            else:
                print(f"情绪分析 API 错误: {response.status_code}")
                
        except Exception as e:
            print(f"情绪分析失败: {e}")
            
        return self._simple_sentiment_analysis(text)
    
    def _simple_sentiment_analysis(self, text: str) -> Dict[str, float]:
        """简单的关键词情绪分析（备用方案）"""
        positive_words = [
            'bullish', 'moon', 'pump', 'buy', 'hodl', 'diamond', 'hands',
            'rocket', 'lambo', 'ath', 'breakout', 'surge', 'rally', 'up',
            'rise', 'gain', 'profit', 'win', 'good', 'great', 'amazing'
        ]
        
        negative_words = [
            'bearish', 'dump', 'sell', 'crash', 'dip', 'rekt', 'fud',
            'panic', 'drop', 'fall', 'correction', 'bubble', 'down',
            'loss', 'bad', 'terrible', 'awful', 'scam', 'rug'
        ]
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        total = positive_count + negative_count
        if total == 0:
            return {'positive': 0.5, 'negative': 0.5, 'neutral': 0.0}
            
        return {
            'positive': positive_count / total,
            'negative': negative_count / total,
            'neutral': 0.0
        }
    
    def generate_summary(self, kol_data: List[Dict], coin_symbol: str) -> str:
        """生成分析总结"""
        if not self.api_token:
            return self._simple_summary(kol_data, coin_symbol)
            
        try:
            # 构建输入文本
            summary_text = f"Analysis of {coin_symbol} mentions by crypto KOLs:\n\n"
            
            for kol in kol_data[:5]:  # 只取前5名
                if kol['coin_mentions'] > 0:
                    summary_text += f"{kol['display_name']} mentioned {coin_symbol} {kol['coin_mentions']} times "
                    summary_text += f"with average {kol['avg_interactions']} interactions. "
                    if 'avg_price_impact' in kol:
                        summary_text += f"Average price impact: {kol['avg_price_impact']:.2f}%. "
                    summary_text += "\n"
            
            # 使用 Hugging Face 总结模型
            api_url = f"https://api-inference.huggingface.co/models/{self.summarization_model}"
            
            payload = {
                "inputs": summary_text,
                "parameters": {
                    "max_length": 200,
                    "min_length": 50,
                    "do_sample": False
                }
            }
            
            response = requests.post(api_url, headers=self.headers, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list) and len(result) > 0:
                    return result[0].get('summary_text', self._simple_summary(kol_data, coin_symbol))
            else:
                print(f"总结生成 API 错误: {response.status_code}")
                
        except Exception as e:
            print(f"总结生成失败: {e}")
            
        return self._simple_summary(kol_data, coin_symbol)
    
    def _simple_summary(self, kol_data: List[Dict], coin_symbol: str) -> str:
        """简单的总结生成（备用方案）"""
        if not kol_data:
            return f"未找到关于 {coin_symbol} 的 KOL 提及数据。"
            
        # 统计数据
        total_mentions = sum(kol['coin_mentions'] for kol in kol_data)
        active_kols = len([kol for kol in kol_data if kol['coin_mentions'] > 0])
        
        if active_kols == 0:
            return f"在分析期间内，没有 KOL 提及 {coin_symbol}。"
        
        # 找出最活跃的 KOL
        top_kol = max(kol_data, key=lambda x: x['coin_mentions'])
        
        # 计算平均影响
        avg_interactions = sum(kol['avg_interactions'] for kol in kol_data if kol['coin_mentions'] > 0) / active_kols
        
        summary = f"""
📊 {coin_symbol} KOL 影响力分析总结

🔍 分析概况：
• 共有 {active_kols} 位 KOL 提及了 {coin_symbol}
• 总提及次数：{total_mentions} 次
• 平均互动数：{avg_interactions:.0f}

👑 最活跃 KOL：
• {top_kol['display_name']} (@{top_kol['username']})
• 提及次数：{top_kol['coin_mentions']} 次
• 平均互动：{top_kol['avg_interactions']:.0f}
"""

        # 添加价格影响信息（如果有）
        if 'avg_price_impact' in top_kol and top_kol['avg_price_impact'] != 0:
            summary += f"• 平均价格影响：{top_kol['avg_price_impact']:.2f}%\n"
        
        # 添加情绪分析
        avg_sentiment = sum(kol['sentiment_score'] for kol in kol_data if kol['coin_mentions'] > 0) / active_kols
        sentiment_desc = "积极" if avg_sentiment > 0 else "消极" if avg_sentiment < 0 else "中性"
        summary += f"\n💭 整体情绪：{sentiment_desc} (评分: {avg_sentiment:.2f})"
        
        return summary
    
    def analyze_tweet_sentiment_batch(self, tweets: List[str]) -> List[Dict]:
        """批量分析推文情绪"""
        results = []
        for tweet in tweets:
            sentiment = self.analyze_sentiment(tweet)
            results.append(sentiment)
            time.sleep(0.1)  # 避免 API 限制
        return results
