"""
币价追踪模块 - 使用 CoinGecko API
"""
import requests
import time
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import diskcache as dc
from config import COINGECKO_API_URL, RATE_LIMIT_DELAY, CACHE_DIR
import os

class PriceTracker:
    def __init__(self):
        self.base_url = COINGECKO_API_URL
        self.cache = dc.Cache(os.path.join(CACHE_DIR, 'price_cache'))
        
    def search_coin(self, query: str) -> Optional[Dict]:
        """搜索币种，返回 CoinGecko ID"""
        cache_key = f"search_{query.lower()}"
        if cache_key in self.cache:
            return self.cache[cache_key]
            
        try:
            url = f"{self.base_url}/search"
            params = {"query": query}
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            coins = data.get('coins', [])
            
            # 寻找最匹配的币种
            for coin in coins:
                if (query.lower() in coin['id'].lower() or 
                    query.lower() in coin['symbol'].lower() or
                    query.lower() in coin['name'].lower()):
                    result = {
                        'id': coin['id'],
                        'symbol': coin['symbol'],
                        'name': coin['name']
                    }
                    self.cache[cache_key] = result
                    return result
                    
            return None
            
        except Exception as e:
            print(f"搜索币种失败: {e}")
            return None
    
    def get_historical_price(self, coin_id: str, timestamp: datetime) -> Optional[float]:
        """获取指定时间的币价"""
        date_str = timestamp.strftime("%d-%m-%Y")
        cache_key = f"price_{coin_id}_{date_str}"
        
        if cache_key in self.cache:
            return self.cache[cache_key]
            
        try:
            url = f"{self.base_url}/coins/{coin_id}/history"
            params = {
                "date": date_str,
                "localization": "false"
            }
            
            time.sleep(RATE_LIMIT_DELAY)  # 限制请求频率
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            price = data.get('market_data', {}).get('current_price', {}).get('usd')
            
            if price:
                self.cache[cache_key] = price
                return price
                
        except Exception as e:
            print(f"获取历史价格失败 {coin_id} {date_str}: {e}")
            
        return None
    
    def calculate_price_impact(self, coin_id: str, tweet_time: datetime, 
                             hours_after: List[int] = [6, 24, 48]) -> Dict[str, float]:
        """计算推文后的价格影响"""
        base_price = self.get_historical_price(coin_id, tweet_time)
        if not base_price:
            return {}
            
        impacts = {}
        for hours in hours_after:
            future_time = tweet_time + timedelta(hours=hours)
            future_price = self.get_historical_price(coin_id, future_time)
            
            if future_price and base_price:
                impact = ((future_price - base_price) / base_price) * 100
                impacts[f"{hours}h"] = round(impact, 2)
                
        return impacts
    
    def get_current_price(self, coin_id: str) -> Optional[Dict]:
        """获取当前价格信息"""
        cache_key = f"current_{coin_id}"
        
        # 缓存5分钟
        if cache_key in self.cache:
            cached_data = self.cache[cache_key]
            if (datetime.now() - cached_data['timestamp']).seconds < 300:
                return cached_data['data']
        
        try:
            url = f"{self.base_url}/simple/price"
            params = {
                "ids": coin_id,
                "vs_currencies": "usd",
                "include_24hr_change": "true",
                "include_market_cap": "true"
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            if coin_id in data:
                result = data[coin_id]
                cache_data = {
                    'data': result,
                    'timestamp': datetime.now()
                }
                self.cache[cache_key] = cache_data
                return result
                
        except Exception as e:
            print(f"获取当前价格失败: {e}")
            
        return None
