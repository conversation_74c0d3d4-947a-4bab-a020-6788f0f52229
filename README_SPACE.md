---
title: ShillRank Agent
emoji: 🤖
colorFrom: blue
colorTo: purple
sdk: gradio
sdk_version: 4.44.0
app_file: agent_app.py
pinned: false
license: mit
---

# 🤖 ShillRank Agent

**智能加密货币KOL影响力分析助手**

基于 LlamaIndex + Hugging Face 构建的对话式AI Agent，专门分析加密货币KOL（意见领袖）的影响力。

## 🎯 Agent 能力

### 💬 智能对话
- 自然语言交互，理解用户的加密货币相关问题
- 支持多轮对话和上下文理解
- 提供个性化的KOL分析建议

### 🛠️ 专业工具
- **CoinGecko Tool**: 获取实时币价和市场数据
- **Twitter Analysis Tool**: 分析KOL推文影响力和情绪
- **KOL Analysis Tool**: 综合分析和排名计算

### 📊 核心功能
- 🔍 币种信息查询和价格追踪
- 📈 KOL影响力排名分析
- 💹 推文对价格影响的计算
- 🐦 社交媒体情绪分析
- 📋 详细分析报告生成

## 💡 使用示例

### 基础查询
```
用户: "你好，请介绍一下你的功能"
Agent: 介绍自己的能力和可用功能
```

### KOL影响力分析
```
用户: "分析PEPE币最近7天的KOL影响力"
Agent: 
1. 搜索PEPE币信息
2. 分析相关KOL的推文
3. 计算影响力排名
4. 生成分析报告
```

### 价格数据查询
```
用户: "查看DOGE币的当前价格和市场数据"
Agent: 获取实时价格、市值、24h变化等数据
```

### 比较分析
```
用户: "比较几个顶级KOL对ETH的影响"
Agent: 分析多个KOL的表现并进行对比
```

## 🏗️ 技术架构

```
┌─────────────────────────────────────────┐
│           Gradio ChatInterface          │
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│            LlamaIndex Agent            │
│  ┌─────────────┐  ┌─────────────────┐  │
│  │   ReAct     │  │   Chat Memory   │  │
│  │  Reasoning  │  │     Buffer      │  │
│  └─────────────┘  └─────────────────┘  │
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│              Tools Layer                │
│  ┌─────────────┐ ┌─────────────────────┐│
│  │ CoinGecko   │ │   Twitter Analysis  ││
│  │    Tool     │ │       Tool          ││
│  └─────────────┘ └─────────────────────┘│
│  ┌─────────────────────────────────────┐ │
│  │        KOL Analysis Tool            │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│            Knowledge Base               │
│  ┌─────────────┐ ┌─────────────────────┐│
│  │   Vector    │ │    Historical       ││
│  │   Store     │ │      Data           ││
│  └─────────────┘ └─────────────────────┘│
└─────────────────────────────────────────┘
```

## 🚀 快速开始

1. **直接对话**: 在聊天框中输入你的问题
2. **使用示例**: 点击示例问题快速开始
3. **查看结果**: Agent会自动调用相关工具并返回分析结果

## ⚠️ 重要声明

- 📊 **数据来源**: Twitter + CoinGecko API
- 🔄 **更新频率**: 实时获取最新数据
- ⚡ **响应时间**: 根据查询复杂度，通常10-30秒
- 💡 **投资建议**: 分析结果仅供参考，不构成投资建议
- 🛡️ **风险提示**: 加密货币投资有风险，请谨慎决策

## 🔧 技术栈

- **AI框架**: LlamaIndex Agent with ReAct
- **语言模型**: Hugging Face Transformers
- **前端界面**: Gradio ChatInterface
- **数据源**: Twitter API + CoinGecko API
- **向量存储**: ChromaDB (可选)
- **部署平台**: Hugging Face Space

## 📈 竞赛优势

1. **实用性强**: 解决真实的加密货币投资需求
2. **技术先进**: 结合最新的Agent技术和多模态数据分析
3. **用户体验**: 直观的对话式界面，易于使用
4. **可扩展性**: 模块化设计，易于添加新功能和数据源
5. **社区价值**: 为加密货币社区提供有价值的分析工具

---

**开发者**: Agent Demo 马拉松参赛作品  
**许可证**: MIT License
