"""
ShillRank 混合版 Agent - Chatbot + 智能Leaderboard展示
结合对话交互和数据可视化的最佳方案
"""

import gradio as gr
import pandas as pd
import plotly.graph_objects as go
import json
import os
from modules.analyzer import ShillRankAnalyzer
from modules.price_tracker import PriceTracker
from modules.kol_scraper import KOL<PERSON>craper
from modules.llm_analyzer import LLMAnalyzer
from utils.helpers import create_ranking_chart, format_kol_table
from config import APP_TITLE, APP_DESCRIPTION


class HybridAgent:
    """混合Agent - 支持对话和可视化展示"""
    
    def __init__(self):
        self.analyzer = ShillRankAnalyzer()
        self.price_tracker = PriceTracker()
        self.kol_scraper = KOLScraper()
        self.llm_analyzer = LLMAnalyzer()
        
        # 对话历史
        self.conversation_history = []
        
        # 最新分析结果缓存
        self.latest_analysis = None
    
    def chat(self, message: str):
        """处理用户消息并返回对话+可视化结果"""
        try:
            # 添加到历史
            self.conversation_history.append({"role": "user", "content": message})
            
            # 分析用户意图并生成响应
            response_text, chart, table = self._process_message(message)
            
            # 添加响应到历史
            self.conversation_history.append({"role": "assistant", "content": response_text})
            
            return response_text, chart, table
            
        except Exception as e:
            error_response = f"抱歉，处理您的请求时出现错误：{str(e)}"
            return error_response, None, pd.DataFrame()
    
    def _process_message(self, message: str):
        """处理消息并返回文本+图表+表格"""
        message_lower = message.lower()
        
        # 问候语
        if any(word in message_lower for word in ['你好', 'hello', 'hi', '介绍']):
            return self._get_greeting(), None, pd.DataFrame()
        
        # 分析币种影响力 - 返回完整可视化
        elif any(word in message_lower for word in ['分析', 'analyze', '影响力', 'kol', '排行榜', '排名']):
            return self._handle_analysis_with_viz(message)
        
        # 查询价格
        elif any(word in message_lower for word in ['价格', 'price', '币价', '市值']):
            return self._handle_price_query(message), None, pd.DataFrame()
        
        # 显示图表/表格
        elif any(word in message_lower for word in ['图表', 'chart', '表格', 'table', '可视化', '显示']):
            return self._show_latest_visualization()
        
        # 帮助信息
        elif any(word in message_lower for word in ['帮助', 'help', '功能', '能力']):
            return self._get_help_info(), None, pd.DataFrame()
        
        else:
            return self._get_default_response(message), None, pd.DataFrame()
    
    def _handle_analysis_with_viz(self, message: str):
        """处理分析请求并返回可视化结果"""
        # 提取币种名称
        coin_name = self._extract_coin_name(message)
        if not coin_name:
            return """
请告诉我你想分析哪个币种？

例如：
• "分析PEPE币的KOL影响力"
• "显示DOGE的排行榜"
• "帮我看看BTC的KOL数据"
""", None, pd.DataFrame()
        
        # 提取天数
        days = self._extract_days(message)
        
        try:
            # 执行分析
            result = self.analyzer.analyze_coin_influence(coin_name, days)
            
            if 'error' in result:
                return f"❌ {result['error']}\n\n请检查币种名称是否正确，或尝试其他币种。", None, pd.DataFrame()
            
            # 缓存结果
            self.latest_analysis = result
            
            # 生成文本响应
            response_text = self._format_analysis_response(result, coin_name, days)
            
            # 生成图表
            chart = None
            table = pd.DataFrame()
            
            if result['kol_rankings']:
                # 创建排行榜图表
                chart = create_ranking_chart(result['kol_rankings'])
                
                # 创建数据表格
                table = format_kol_table(result['kol_rankings'])
            
            return response_text, chart, table
            
        except Exception as e:
            return f"分析过程中出现错误：{str(e)}\n\n请稍后重试或尝试其他币种。", None, pd.DataFrame()
    
    def _show_latest_visualization(self):
        """显示最新的可视化结果"""
        if not self.latest_analysis:
            return """
暂无分析数据可显示。

请先进行分析，例如：
• "分析PEPE币的KOL影响力"
• "分析DOGE最近7天的数据"
""", None, pd.DataFrame()
        
        result = self.latest_analysis
        
        # 生成图表和表格
        chart = None
        table = pd.DataFrame()
        
        if result['kol_rankings']:
            chart = create_ranking_chart(result['kol_rankings'])
            table = format_kol_table(result['kol_rankings'])
        
        coin_info = result['coin_info']
        response_text = f"""
📊 **{coin_info['name']} ({coin_info['symbol'].upper()}) 可视化数据**

图表和表格已更新显示最新分析结果！

💡 你还可以：
• 询问具体KOL的详细信息
• 分析其他币种
• 查看价格数据
"""
        
        return response_text, chart, table
    
    def _get_greeting(self) -> str:
        """获取问候响应"""
        return """
你好！我是CryptoKOL Agent 🤖

我专门分析加密货币KOL（意见领袖）的影响力，可以为你提供：

📊 **智能分析**
• KOL影响力排行榜
• 实时价格数据查询
• 推文情绪分析
• 综合影响力评分

💡 **交互方式**
• 💬 自然语言对话
• 📈 智能图表展示
• 📋 详细数据表格

🚀 **开始体验**
试试说："分析PEPE币的KOL影响力"，我会为你生成完整的分析报告和可视化图表！
"""
    
    def _handle_price_query(self, message: str) -> str:
        """处理价格查询"""
        coin_name = self._extract_coin_name(message)
        if not coin_name:
            return "请告诉我你想查询哪个币种的价格？例如：'查看PEPE的价格'"
        
        try:
            # 搜索币种
            coin_info = self.price_tracker.search_coin(coin_name)
            if not coin_info:
                return f"未找到币种：{coin_name}"
            
            # 获取价格
            price_data = self.price_tracker.get_current_price(coin_info['id'])
            if not price_data:
                return f"无法获取 {coin_name} 的价格数据"
            
            return self._format_price_response(coin_info, price_data)
            
        except Exception as e:
            return f"价格查询失败：{str(e)}"
    
    def _get_help_info(self) -> str:
        """获取帮助信息"""
        return """
🤖 **CryptoKOL Agent 使用指南**

📋 **主要功能**
1. 🔍 币种信息查询 - "查看PEPE的价格"
2. 📊 KOL影响力分析 - "分析DOGE的KOL影响力" 
3. 📈 可视化展示 - 自动生成图表和表格
4. 💬 智能对话 - 自然语言交互

🎯 **特色功能**
• **智能可视化**: 分析结果自动生成图表
• **实时数据**: 获取最新的价格和推文数据
• **多维分析**: 价格影响+互动数据+情绪分析

💡 **使用技巧**
• 直接说出币种名称，如PEPE、DOGE、BTC
• 可以指定分析天数，如"最近7天"
• 说"显示图表"可重新展示最新分析结果

⚠️ **注意事项**
• 分析结果仅供参考，不构成投资建议
• 数据来源：Twitter + CoinGecko
• 首次分析可能需要等待数据加载

有什么具体问题吗？
"""
    
    def _get_default_response(self, message: str) -> str:
        """默认响应"""
        return f"""
我理解你想了解："{message}"

作为专业的加密货币KOL影响力分析助手，我可以帮你：

🔍 **深度分析**
• "分析PEPE币的KOL影响力" - 获取完整排行榜
• "查看DOGE的价格数据" - 实时市场信息

📊 **可视化展示**  
• "显示图表" - 查看最新分析图表
• "显示表格" - 查看详细数据表格

💡 **智能交互**
• 支持自然语言提问
• 自动生成可视化结果
• 提供专业分析建议

请告诉我你想分析哪个币种？
"""
    
    # 复用之前的辅助方法
    def _extract_coin_name(self, message: str) -> str:
        """从消息中提取币种名称"""
        common_coins = [
            'BTC', 'ETH', 'DOGE', 'PEPE', 'SHIB', 'ADA', 'DOT', 'LINK',
            'UNI', 'MATIC', 'SOL', 'AVAX', 'ATOM', 'FTM', 'NEAR'
        ]
        
        message_upper = message.upper()
        
        for coin in common_coins:
            if coin in message_upper:
                return coin
        
        import re
        dollar_pattern = r'\$([A-Z]{2,10})'
        matches = re.findall(dollar_pattern, message_upper)
        if matches:
            return matches[0]
        
        return ""
    
    def _extract_days(self, message: str) -> int:
        """从消息中提取天数"""
        import re
        
        day_patterns = [
            r'(\d+)\s*天',
            r'(\d+)\s*day',
            r'最近\s*(\d+)',
            r'过去\s*(\d+)'
        ]
        
        for pattern in day_patterns:
            matches = re.findall(pattern, message)
            if matches:
                days = int(matches[0])
                return min(max(days, 1), 30)
        
        return 7
    
    def _format_analysis_response(self, result: dict, coin_name: str, days: int) -> str:
        """格式化分析响应"""
        coin_info = result['coin_info']
        kol_rankings = result['kol_rankings']
        
        response = f"""
✅ **{coin_info['name']} ({coin_info['symbol'].upper()}) KOL影响力分析完成！**

📊 **基本信息**
• 币种：{coin_info['name']} ({coin_info['symbol'].upper()})
• 分析天数：{days} 天
• 活跃KOL：{len(kol_rankings)} 位

📈 **可视化结果**
• 📊 排行榜图表已生成
• 📋 详细数据表格已更新
• 🎯 综合评分可视化展示
"""
        
        # 添加价格信息
        if 'current_price' in result and result['current_price']:
            price_data = result['current_price']
            price_usd = price_data.get('usd', 0)
            change_24h = price_data.get('usd_24h_change', 0)
            
            response += f"""
💰 **当前价格**
• 价格：${price_usd:.6f}
• 24h变化：{change_24h:.2f}%
"""
        
        # 添加前3名KOL
        if kol_rankings:
            response += "\n🏆 **影响力前3名**\n"
            for i, kol in enumerate(kol_rankings[:3], 1):
                response += f"{i}. **{kol['display_name']}** - 评分: {kol['total_score']:.1f}\n"
        
        # 添加AI总结
        if 'summary' in result:
            response += f"\n🤖 **AI分析总结**\n{result['summary']}"
        
        response += "\n\n💡 **查看更多**\n• 右侧图表显示完整排行榜\n• 下方表格显示详细数据\n• 可以继续询问其他币种"
        
        return response
    
    def _format_price_response(self, coin_info: dict, price_data: dict) -> str:
        """格式化价格响应"""
        price_usd = price_data.get('usd', 0)
        change_24h = price_data.get('usd_24h_change', 0)
        market_cap = price_data.get('usd_market_cap', 0)
        volume_24h = price_data.get('usd_24h_vol', 0)
        
        response = f"""
💰 **{coin_info['name']} ({coin_info['symbol'].upper()}) 价格信息**

📈 **实时数据**
• 当前价格：${price_usd:.6f}
• 24h变化：{change_24h:.2f}%
• 市值：${market_cap:,.0f}
• 24h交易量：${volume_24h:,.0f}

💡 **深度分析**
想了解KOL对价格的影响？试试：
"分析{coin_info['symbol'].upper()}的KOL影响力"
"""
        
        return response
    
    def reset_conversation(self):
        """重置对话历史"""
        self.conversation_history = []
        self.latest_analysis = None


def create_hybrid_interface():
    """创建混合界面 - Chatbot + 可视化"""
    
    # 初始化Agent
    agent = HybridAgent()
    
    def chat_with_agent(message, history):
        """与Agent对话并更新可视化"""
        if not message.strip():
            return history, "", None, pd.DataFrame()
        
        try:
            # 获取Agent响应
            response_text, chart, table = agent.chat(message)
            
            # 更新对话历史
            history.append([message, response_text])
            
            return history, "", chart, table
        
        except Exception as e:
            error_response = f"抱歉，处理您的请求时出现错误：{str(e)}"
            history.append([message, error_response])
            return history, "", None, pd.DataFrame()
    
    def reset_all():
        """重置所有内容"""
        agent.reset_conversation()
        return [], "", None, pd.DataFrame()
    
    # 创建Gradio界面
    with gr.Blocks(
        title="ShillRank Hybrid Agent",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1400px !important;
        }
        .main-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        """
    ) as app:
        
        # 标题和描述
        gr.HTML(f"""
        <div class="main-header">
            <h1>🤖 {APP_TITLE} Hybrid Agent</h1>
            <p>智能对话 + 可视化分析的混合Agent体验</p>
            <p>💬 自然语言交互 | 📊 智能图表展示 | 📋 详细数据表格</p>
        </div>
        """)
        
        # 主要内容区域
        with gr.Row():
            # 左侧：对话区域
            with gr.Column(scale=2):
                chatbot = gr.Chatbot(
                    label="💬 与CryptoKOL Agent对话",
                    height=400,
                    show_label=True,
                    container=True,
                    type="messages"
                )
                
                with gr.Row():
                    msg_input = gr.Textbox(
                        label="输入消息",
                        placeholder="例如：分析PEPE币最近的KOL影响力...",
                        scale=4,
                        container=False
                    )
                    send_btn = gr.Button("发送", variant="primary", scale=1)
                
                with gr.Row():
                    clear_btn = gr.Button("清空对话", variant="secondary")
                    reset_btn = gr.Button("重置全部", variant="secondary")
            
            # 右侧：可视化区域
            with gr.Column(scale=2):
                chart_output = gr.Plot(
                    label="📊 KOL影响力排行榜",
                    height=400
                )
                
                table_output = gr.Dataframe(
                    label="📋 详细数据表格",
                    height=300,
                    interactive=False,
                    wrap=True
                )
        
        # 示例问题
        gr.Examples(
            examples=[
                "你好，请介绍一下你的功能",
                "分析PEPE币最近7天的KOL影响力",
                "查看DOGE币的当前价格和市场数据",
                "显示图表和表格",
                "帮我分析一下BTC的KOL排行榜",
                "分析SHIB币过去14天的数据"
            ],
            inputs=msg_input,
            label="💡 示例问题"
        )
        
        # 绑定事件
        def submit_message(message, history):
            if message.strip():
                return chat_with_agent(message, history)
            return history, message, None, pd.DataFrame()
        
        # 发送消息
        send_btn.click(
            fn=submit_message,
            inputs=[msg_input, chatbot],
            outputs=[chatbot, msg_input, chart_output, table_output]
        )
        
        # 回车发送
        msg_input.submit(
            fn=submit_message,
            inputs=[msg_input, chatbot],
            outputs=[chatbot, msg_input, chart_output, table_output]
        )
        
        # 清空对话
        clear_btn.click(
            fn=lambda: ([], ""),
            outputs=[chatbot, msg_input]
        )
        
        # 重置全部
        reset_btn.click(
            fn=reset_all,
            outputs=[chatbot, msg_input, chart_output, table_output]
        )
        
        # 页脚信息
        gr.HTML("""
        <div style="text-align: center; margin-top: 2rem; padding: 1rem; border-top: 1px solid #eee;">
            <p>🤖 <strong>ShillRank Hybrid Agent</strong> - 对话式AI + 智能可视化</p>
            <p>🔧 技术栈: 自然语言处理 + 数据分析 + 智能图表 + Gradio</p>
            <p>📊 数据源: Twitter + CoinGecko | 🚀 部署: Hugging Face Space</p>
        </div>
        """)
    
    return app


if __name__ == "__main__":
    # 创建应用
    app = create_hybrid_interface()
    
    # 启动应用
    app.launch(
        share=False,
        debug=False
    )
