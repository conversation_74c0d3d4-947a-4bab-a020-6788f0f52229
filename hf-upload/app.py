"""
ShillRank 简化版 Agent 应用
当LlamaIndex不可用时的备用方案
"""

import gradio as gr
import json
import os
from modules.analyzer import ShillRankAnalyzer
from modules.price_tracker import PriceTracker
from modules.kol_scraper import K<PERSON><PERSON>craper
from modules.llm_analyzer import LLMAnalyzer
from config import APP_TITLE, APP_DESCRIPTION


class SimpleAgent:
    """简化版Agent类"""
    
    def __init__(self):
        self.analyzer = ShillRankAnalyzer()
        self.price_tracker = PriceTracker()
        self.kol_scraper = KOLScraper()
        self.llm_analyzer = LLMAnalyzer()
        
        # 对话历史
        self.conversation_history = []
    
    def chat(self, message: str) -> str:
        """处理用户消息"""
        try:
            # 添加到历史
            self.conversation_history.append({"role": "user", "content": message})
            
            # 分析用户意图
            response = self._process_message(message)
            
            # 添加响应到历史
            self.conversation_history.append({"role": "assistant", "content": response})
            
            return response
            
        except Exception as e:
            error_response = f"抱歉，处理您的请求时出现错误：{str(e)}"
            return error_response
    
    def _process_message(self, message: str) -> str:
        """处理消息并生成响应"""
        message_lower = message.lower()
        
        # 问候语
        if any(word in message_lower for word in ['你好', 'hello', 'hi', '介绍']):
            return self._get_greeting()
        
        # 分析币种影响力
        elif any(word in message_lower for word in ['分析', 'analyze', '影响力', 'kol']):
            return self._handle_analysis_request(message)
        
        # 查询价格
        elif any(word in message_lower for word in ['价格', 'price', '币价', '市值']):
            return self._handle_price_query(message)
        
        # 查询KOL信息
        elif any(word in message_lower for word in ['kol', '推文', 'tweet', '情绪']):
            return self._handle_kol_query(message)
        
        # 帮助信息
        elif any(word in message_lower for word in ['帮助', 'help', '功能', '能力']):
            return self._get_help_info()
        
        else:
            return self._get_default_response(message)
    
    def _get_greeting(self) -> str:
        """获取问候响应"""
        return """
你好！我是CryptoKOL Agent 🤖

我专门分析加密货币KOL（意见领袖）的影响力，可以帮你：

📊 **核心功能**
• 分析KOL对特定币种的影响力排名
• 计算推文对价格的影响
• 提供市场数据和趋势分析
• 生成详细的分析报告

💡 **使用示例**
• "分析PEPE币最近7天的KOL影响力"
• "查看DOGE币的当前价格"
• "帮我分析BTC的KOL排行榜"

请告诉我你想了解什么？
"""
    
    def _handle_analysis_request(self, message: str) -> str:
        """处理分析请求"""
        # 提取币种名称
        coin_name = self._extract_coin_name(message)
        if not coin_name:
            return """
请告诉我你想分析哪个币种？

例如：
• "分析PEPE币的KOL影响力"
• "分析DOGE最近的表现"
• "帮我看看BTC的KOL数据"
"""
        
        # 提取天数
        days = self._extract_days(message)
        
        try:
            # 执行分析
            result = self.analyzer.analyze_coin_influence(coin_name, days)
            
            if 'error' in result:
                return f"❌ {result['error']}\n\n请检查币种名称是否正确，或尝试其他币种。"
            
            # 格式化响应
            return self._format_analysis_response(result, coin_name, days)
            
        except Exception as e:
            return f"分析过程中出现错误：{str(e)}\n\n请稍后重试或尝试其他币种。"
    
    def _handle_price_query(self, message: str) -> str:
        """处理价格查询"""
        coin_name = self._extract_coin_name(message)
        if not coin_name:
            return "请告诉我你想查询哪个币种的价格？例如：'查看PEPE的价格'"
        
        try:
            # 搜索币种
            coin_info = self.price_tracker.search_coin(coin_name)
            if not coin_info:
                return f"未找到币种：{coin_name}"
            
            # 获取价格
            price_data = self.price_tracker.get_current_price(coin_info['id'])
            if not price_data:
                return f"无法获取 {coin_name} 的价格数据"
            
            return self._format_price_response(coin_info, price_data)
            
        except Exception as e:
            return f"价格查询失败：{str(e)}"
    
    def _handle_kol_query(self, message: str) -> str:
        """处理KOL查询"""
        return """
KOL分析功能包括：

📊 **可分析的KOL类型**
• 加密货币意见领袖
• 区块链项目创始人
• 交易所高管
• 知名投资者

💡 **分析维度**
• 推文影响力评分
• 价格影响计算
• 互动数据统计
• 情绪分析

请告诉我具体想分析什么，例如：
"分析PEPE币的KOL影响力排名"
"""
    
    def _get_help_info(self) -> str:
        """获取帮助信息"""
        return """
🤖 **CryptoKOL Agent 使用指南**

📋 **主要功能**
1. 🔍 币种信息查询 - "查看PEPE的价格"
2. 📊 KOL影响力分析 - "分析DOGE的KOL影响力"
3. 💹 价格影响计算 - "计算推文对价格的影响"
4. 📈 综合排名报告 - "生成BTC的分析报告"

💡 **使用技巧**
• 直接说出币种名称，如PEPE、DOGE、BTC
• 可以指定分析天数，如"最近7天"、"过去30天"
• 支持中英文混合输入

⚠️ **注意事项**
• 分析结果仅供参考，不构成投资建议
• 数据来源：Twitter + CoinGecko
• 部分功能可能需要等待数据加载

有什么具体问题吗？
"""
    
    def _get_default_response(self, message: str) -> str:
        """默认响应"""
        return f"""
我理解你想了解："{message}"

我是专门分析加密货币KOL影响力的AI助手。请尝试这样问我：

🔍 **币种分析**
• "分析PEPE币的KOL影响力"
• "查看DOGE的价格数据"

📊 **KOL研究**
• "帮我分析BTC的KOL排行榜"
• "比较几个KOL的表现"

💡 **其他功能**
• "介绍你的功能" - 了解我的能力
• "帮助" - 查看使用指南

请告诉我你想了解什么？
"""
    
    def _extract_coin_name(self, message: str) -> str:
        """从消息中提取币种名称"""
        # 常见币种列表
        common_coins = [
            'BTC', 'ETH', 'DOGE', 'PEPE', 'SHIB', 'ADA', 'DOT', 'LINK',
            'UNI', 'MATIC', 'SOL', 'AVAX', 'ATOM', 'FTM', 'NEAR'
        ]
        
        message_upper = message.upper()
        
        # 查找币种符号
        for coin in common_coins:
            if coin in message_upper:
                return coin
        
        # 查找带$符号的币种
        import re
        dollar_pattern = r'\$([A-Z]{2,10})'
        matches = re.findall(dollar_pattern, message_upper)
        if matches:
            return matches[0]
        
        return ""
    
    def _extract_days(self, message: str) -> int:
        """从消息中提取天数"""
        import re
        
        # 查找数字+天的模式
        day_patterns = [
            r'(\d+)\s*天',
            r'(\d+)\s*day',
            r'最近\s*(\d+)',
            r'过去\s*(\d+)'
        ]
        
        for pattern in day_patterns:
            matches = re.findall(pattern, message)
            if matches:
                days = int(matches[0])
                return min(max(days, 1), 30)  # 限制在1-30天
        
        return 7  # 默认7天
    
    def _format_analysis_response(self, result: dict, coin_name: str, days: int) -> str:
        """格式化分析响应"""
        coin_info = result['coin_info']
        kol_rankings = result['kol_rankings']
        
        response = f"""
✅ **{coin_info['name']} ({coin_info['symbol'].upper()}) KOL影响力分析完成！**

📊 **基本信息**
• 币种：{coin_info['name']} ({coin_info['symbol'].upper()})
• 分析天数：{days} 天
• 活跃KOL：{len(kol_rankings)} 位
"""
        
        # 添加价格信息
        if 'current_price' in result and result['current_price']:
            price_data = result['current_price']
            price_usd = price_data.get('usd', 0)
            change_24h = price_data.get('usd_24h_change', 0)
            
            response += f"""
💰 **当前价格**
• 价格：${price_usd:.6f}
• 24h变化：{change_24h:.2f}%
"""
        
        # 添加排行榜
        if kol_rankings:
            response += "\n🏆 **KOL影响力排行榜（前5名）**\n"
            for i, kol in enumerate(kol_rankings[:5], 1):
                response += f"""
{i}. **{kol['display_name']}** (@{kol['username']})
   • 综合评分：{kol['total_score']:.1f}
   • 提及次数：{kol['coin_mentions']} 次
   • 平均互动：{kol['avg_interactions']:.0f}
"""
                if 'avg_price_impact' in kol and kol['avg_price_impact'] != 0:
                    response += f"   • 平均价格影响：{kol['avg_price_impact']:.2f}%\n"
        
        # 添加AI总结
        if 'summary' in result:
            response += f"\n🤖 **AI分析总结**\n{result['summary']}"
        
        response += "\n\n💡 想了解更多？可以问我：\n• \"查看详细数据\"\n• \"分析其他币种\"\n• \"比较不同KOL\""
        
        return response
    
    def _format_price_response(self, coin_info: dict, price_data: dict) -> str:
        """格式化价格响应"""
        price_usd = price_data.get('usd', 0)
        change_24h = price_data.get('usd_24h_change', 0)
        market_cap = price_data.get('usd_market_cap', 0)
        volume_24h = price_data.get('usd_24h_vol', 0)
        
        response = f"""
💰 **{coin_info['name']} ({coin_info['symbol'].upper()}) 价格信息**

📈 **实时数据**
• 当前价格：${price_usd:.6f}
• 24h变化：{change_24h:.2f}%
• 市值：${market_cap:,.0f}
• 24h交易量：${volume_24h:,.0f}

💡 想了解更多？可以问我：
• "分析{coin_info['symbol'].upper()}的KOL影响力"
• "查看{coin_info['symbol'].upper()}的历史表现"
"""
        
        return response
    
    def reset_conversation(self):
        """重置对话历史"""
        self.conversation_history = []


def create_simple_agent_interface():
    """创建简化版Agent界面"""
    
    # 初始化Agent
    agent = SimpleAgent()
    
    def chat_with_agent(message, history):
        """与Agent对话"""
        if not message.strip():
            return history, ""
        
        try:
            # 获取Agent响应
            response = agent.chat(message)
            
            # 更新对话历史
            history.append([message, response])
            
            return history, ""
        
        except Exception as e:
            error_response = f"抱歉，处理您的请求时出现错误：{str(e)}"
            history.append([message, error_response])
            return history, ""
    
    def reset_chat():
        """重置对话"""
        agent.reset_conversation()
        return [], ""
    
    # 创建Gradio界面
    with gr.Blocks(
        title="ShillRank Agent",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1000px !important;
        }
        .main-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        """
    ) as app:
        
        # 标题和描述
        gr.HTML(f"""
        <div class="main-header">
            <h1>🤖 {APP_TITLE} Agent</h1>
            <p>智能加密货币KOL影响力分析助手</p>
            <p>基于自然语言处理的对话式AI分析工具</p>
        </div>
        """)
        
        # 主要对话区域
        with gr.Row():
            with gr.Column(scale=4):
                # 聊天界面
                chatbot = gr.Chatbot(
                    label="💬 与CryptoKOL Agent对话",
                    height=500,
                    show_label=True,
                    container=True,
                    type="messages"
                )
                
                with gr.Row():
                    msg_input = gr.Textbox(
                        label="输入消息",
                        placeholder="例如：分析PEPE币最近的KOL影响力...",
                        scale=4,
                        container=False
                    )
                    send_btn = gr.Button("发送", variant="primary", scale=1)
                
                with gr.Row():
                    clear_btn = gr.Button("清空对话", variant="secondary")
                    reset_btn = gr.Button("重置Agent", variant="secondary")
            
            with gr.Column(scale=1):
                # 侧边栏
                gr.HTML("""
                <div style="padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                    <h3>🎯 Agent能力</h3>
                    <ul>
                        <li>🔍 币种信息查询</li>
                        <li>📊 KOL影响力分析</li>
                        <li>💹 价格影响计算</li>
                        <li>🐦 推文情绪分析</li>
                        <li>📈 综合排名报告</li>
                    </ul>
                    
                    <h3>💡 使用示例</h3>
                    <ul>
                        <li>"分析PEPE币的KOL影响力"</li>
                        <li>"查看DOGE最近的价格数据"</li>
                        <li>"比较几个KOL的表现"</li>
                        <li>"生成BTC的分析报告"</li>
                    </ul>
                    
                    <h3>⚠️ 注意事项</h3>
                    <p>• 分析结果仅供参考<br>
                    • 不构成投资建议<br>
                    • 数据可能有延迟</p>
                </div>
                """)
        
        # 示例问题
        gr.Examples(
            examples=[
                "你好，请介绍一下你的功能",
                "分析PEPE币最近7天的KOL影响力",
                "查看DOGE币的当前价格和市场数据",
                "帮我分析一下BTC的KOL排行榜",
                "比较几个顶级KOL对ETH的影响",
                "生成SHIB币的详细分析报告"
            ],
            inputs=msg_input,
            label="💡 示例问题"
        )
        
        # 绑定事件
        def submit_message(message, history):
            if message.strip():
                return chat_with_agent(message, history)
            return history, message
        
        # 发送消息
        send_btn.click(
            fn=submit_message,
            inputs=[msg_input, chatbot],
            outputs=[chatbot, msg_input]
        )
        
        # 回车发送
        msg_input.submit(
            fn=submit_message,
            inputs=[msg_input, chatbot],
            outputs=[chatbot, msg_input]
        )
        
        # 清空对话
        clear_btn.click(
            fn=lambda: ([], ""),
            outputs=[chatbot, msg_input]
        )
        
        # 重置Agent
        reset_btn.click(
            fn=reset_chat,
            outputs=[chatbot, msg_input]
        )
        
        # 页脚信息
        gr.HTML("""
        <div style="text-align: center; margin-top: 2rem; padding: 1rem; border-top: 1px solid #eee;">
            <p>🤖 <strong>ShillRank Agent</strong> - 智能加密货币KOL影响力分析助手</p>
            <p>🔧 技术栈: 自然语言处理 + 数据分析 + Gradio ChatInterface</p>
            <p>📊 数据源: Twitter + CoinGecko | 🚀 部署: Hugging Face Space</p>
        </div>
        """)
    
    return app


if __name__ == "__main__":
    # 创建应用
    app = create_simple_agent_interface()
    
    # 启动应用 - Hugging Face Space 配置
    app.launch(
        share=False,
        debug=False
    )
