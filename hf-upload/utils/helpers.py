"""
工具函数
"""
import os
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime
from typing import List, Dict

def ensure_cache_dir():
    """确保缓存目录存在"""
    cache_dir = "data/cache"
    if not os.path.exists(cache_dir):
        os.makedirs(cache_dir, exist_ok=True)

def format_number(num: float, decimal_places: int = 2) -> str:
    """格式化数字显示"""
    if abs(num) >= 1_000_000:
        return f"{num/1_000_000:.{decimal_places}f}M"
    elif abs(num) >= 1_000:
        return f"{num/1_000:.{decimal_places}f}K"
    else:
        return f"{num:.{decimal_places}f}"

def create_ranking_chart(kol_rankings: List[Dict]) -> go.Figure:
    """创建 KOL 排行榜图表"""
    if not kol_rankings:
        fig = go.Figure()
        fig.add_annotation(
            text="暂无数据",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False,
            font=dict(size=20)
        )
        return fig
    
    # 准备数据
    names = [kol['display_name'] for kol in kol_rankings[:10]]  # 只显示前10名
    total_scores = [kol['scores']['total'] for kol in kol_rankings[:10]]
    price_scores = [kol['scores']['price_impact'] for kol in kol_rankings[:10]]
    interaction_scores = [kol['scores']['interactions'] for kol in kol_rankings[:10]]
    
    # 创建堆叠条形图
    fig = go.Figure()
    
    fig.add_trace(go.Bar(
        name='价格影响',
        y=names,
        x=price_scores,
        orientation='h',
        marker_color='#ff6b6b'
    ))
    
    fig.add_trace(go.Bar(
        name='互动影响',
        y=names,
        x=interaction_scores,
        orientation='h',
        marker_color='#4ecdc4'
    ))
    
    fig.update_layout(
        title='KOL 影响力评分排行榜',
        xaxis_title='评分',
        yaxis_title='KOL',
        barmode='group',
        height=500,
        font=dict(family="Arial, sans-serif", size=12)
    )
    
    return fig

def create_price_impact_chart(kol_rankings: List[Dict]) -> go.Figure:
    """创建价格影响对比图"""
    if not kol_rankings:
        return go.Figure()
    
    names = [kol['display_name'] for kol in kol_rankings[:10]]
    impacts = [kol.get('avg_price_impact', 0) for kol in kol_rankings[:10]]
    
    # 根据影响是正负设置颜色
    colors = ['green' if impact >= 0 else 'red' for impact in impacts]
    
    fig = go.Figure(data=[
        go.Bar(
            x=names,
            y=impacts,
            marker_color=colors,
            text=[f"{impact:.2f}%" for impact in impacts],
            textposition='auto'
        )
    ])
    
    fig.update_layout(
        title='KOL 平均价格影响力',
        xaxis_title='KOL',
        yaxis_title='价格影响 (%)',
        height=400,
        xaxis_tickangle=-45
    )
    
    return fig

def create_interaction_chart(kol_rankings: List[Dict]) -> go.Figure:
    """创建互动数对比图"""
    if not kol_rankings:
        return go.Figure()
    
    names = [kol['display_name'] for kol in kol_rankings[:10]]
    interactions = [kol['avg_interactions'] for kol in kol_rankings[:10]]
    
    fig = go.Figure(data=[
        go.Bar(
            x=names,
            y=interactions,
            marker_color='#74b9ff',
            text=[format_number(inter) for inter in interactions],
            textposition='auto'
        )
    ])
    
    fig.update_layout(
        title='KOL 平均互动数',
        xaxis_title='KOL',
        yaxis_title='平均互动数',
        height=400,
        xaxis_tickangle=-45
    )
    
    return fig

def create_mentions_frequency_chart(kol_rankings: List[Dict]) -> go.Figure:
    """创建提及频次图表"""
    if not kol_rankings:
        return go.Figure()
    
    names = [kol['display_name'] for kol in kol_rankings[:10]]
    mentions = [kol['coin_mentions'] for kol in kol_rankings[:10]]
    
    fig = go.Figure(data=[
        go.Bar(
            x=names,
            y=mentions,
            marker_color='#fd79a8',
            text=mentions,
            textposition='auto'
        )
    ])
    
    fig.update_layout(
        title='KOL 提及频次',
        xaxis_title='KOL',
        yaxis_title='提及次数',
        height=400,
        xaxis_tickangle=-45
    )
    
    return fig

def format_kol_table(kol_rankings: List[Dict]) -> pd.DataFrame:
    """格式化 KOL 排行榜表格"""
    if not kol_rankings:
        return pd.DataFrame()
    
    table_data = []
    for kol in kol_rankings:
        row = {
            '排名': kol['rank'],
            'KOL': kol['display_name'],
            '用户名': f"@{kol['username']}",
            '分类': kol.get('category', '未知'),
            '提及次数': kol['coin_mentions'],
            '平均互动': format_number(kol['avg_interactions']),
            '价格影响': f"{kol.get('avg_price_impact', 0):.2f}%",
            '情绪评分': f"{kol['sentiment_score']:.2f}",
            '综合评分': f"{kol['scores']['total']:.2f}"
        }
        table_data.append(row)
    
    return pd.DataFrame(table_data)

def export_results(analysis_result: Dict, filename: str = None) -> str:
    """导出分析结果"""
    if not filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        coin_symbol = analysis_result.get('coin_info', {}).get('symbol', 'unknown')
        filename = f"shillrank_{coin_symbol}_{timestamp}.json"
    
    filepath = os.path.join("data", filename)
    
    # 确保目录存在
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    
    # 保存结果
    import json
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, ensure_ascii=False, indent=2, default=str)
    
    return filepath
