"""
ShillRank 演示版本 - 简化的 Gradio 应用
"""
import gradio as gr
import pandas as pd
from datetime import datetime
import random

def demo_analysis(coin_query: str, days: int):
    """演示分析功能"""
    if not coin_query.strip():
        return "❌ 请输入币种名称", pd.DataFrame()
    
    # 模拟分析过程
    coin_name = coin_query.upper()
    
    # 生成演示数据
    demo_kols = [
        {"KOL": "Elon Musk", "用户名": "@elonmusk", "提及次数": 3, "平均互动": "45.2K", "价格影响": "12.5%", "综合评分": "95.8"},
        {"KOL": "Vitalik Buterin", "用户名": "@VitalikButerin", "提及次数": 1, "平均互动": "28.1K", "价格影响": "8.3%", "综合评分": "78.4"},
        {"KOL": "CZ Binance", "用户名": "@cz_binance", "提及次数": 2, "平均互动": "35.7K", "价格影响": "6.7%", "综合评分": "82.1"},
        {"KOL": "<PERSON> Saylor", "用户名": "@saylor", "提及次数": 1, "平均互动": "22.3K", "价格影响": "4.2%", "综合评分": "65.9"},
        {"KOL": "Anthony Pompliano", "用户名": "@APompliano", "提及次数": 4, "平均互动": "18.9K", "价格影响": "3.8%", "综合评分": "71.2"}
    ]
    
    # 添加排名
    for i, kol in enumerate(demo_kols):
        kol["排名"] = i + 1
    
    df = pd.DataFrame(demo_kols)
    
    # 重新排列列的顺序
    df = df[["排名", "KOL", "用户名", "提及次数", "平均互动", "价格影响", "综合评分"]]
    
    status_info = f"""
✅ 分析完成！

🪙 **币种信息**
• 名称: {coin_name}
• 分析天数: {days} 天
• 活跃 KOL: {len(demo_kols)} 位

📊 **分析结果**
• 总提及次数: {sum(int(kol['提及次数']) for kol in demo_kols)}
• 平均价格影响: +7.1%
• 最活跃 KOL: {demo_kols[0]['KOL']}

🤖 **AI 总结**
在过去 {days} 天内，{coin_name} 受到了多位知名 KOL 的关注。
{demo_kols[0]['KOL']} 以 {demo_kols[0]['提及次数']} 次提及和 {demo_kols[0]['价格影响']} 的价格影响位居榜首。
整体来看，KOL 们对 {coin_name} 持积极态度，平均带来了正向的价格影响。

⚠️ **注意**: 这是演示数据，仅用于展示系统功能。
"""
    
    return status_info, df

def create_demo_interface():
    """创建演示界面"""
    
    with gr.Blocks(
        title="ShillRank Demo",
        theme=gr.themes.Soft()
    ) as app:
        
        # 标题
        gr.HTML("""
        <div style="text-align: center; margin-bottom: 2rem;">
            <h1>💥 ShillRank - 币圈 KOL 影响力排行榜</h1>
            <p>🧠 基于推文和行情数据，量化分析币圈 KOL 喊单影响力</p>
            <p style="color: orange;">⚠️ 当前为演示模式，显示模拟数据</p>
        </div>
        """)
        
        # 输入区域
        with gr.Row():
            with gr.Column(scale=2):
                coin_input = gr.Textbox(
                    label="🪙 币种名称",
                    placeholder="输入币种名称，如: PEPE, DOGE, BTC...",
                    value="PEPE"
                )
            
            with gr.Column(scale=1):
                days_input = gr.Slider(
                    minimum=1,
                    maximum=30,
                    value=7,
                    step=1,
                    label="📅 分析天数"
                )
            
            with gr.Column(scale=1):
                analyze_btn = gr.Button(
                    "🚀 开始分析",
                    variant="primary",
                    size="lg"
                )
        
        # 结果展示
        status_output = gr.Markdown(label="📊 分析状态")
        table_output = gr.Dataframe(
            label="🏆 KOL 影响力排行榜",
            interactive=False,
            wrap=True
        )
        
        # 示例
        gr.Examples(
            examples=[
                ["PEPE", 7],
                ["DOGE", 14],
                ["SHIB", 7],
                ["BTC", 30],
                ["ETH", 14]
            ],
            inputs=[coin_input, days_input],
            label="💡 示例"
        )
        
        # 绑定事件
        analyze_btn.click(
            fn=demo_analysis,
            inputs=[coin_input, days_input],
            outputs=[status_output, table_output]
        )
        
        # 页脚
        gr.HTML("""
        <div style="text-align: center; margin-top: 2rem; padding: 1rem; border-top: 1px solid #eee;">
            <p>🔧 技术栈: Gradio + snscrape + CoinGecko API + Hugging Face</p>
            <p>📊 数据来源: Twitter + CoinGecko | 🤖 AI 分析: Hugging Face Transformers</p>
            <p style="color: red;">⚠️ 免责声明: 本工具仅用于数据分析和研究目的，不构成任何投资建议。</p>
        </div>
        """)
    
    return app

if __name__ == "__main__":
    # 创建并启动演示应用
    demo_app = create_demo_interface()
    
    print("🚀 启动 ShillRank 演示应用...")
    print("📊 当前为演示模式，显示模拟数据")
    print("🔗 应用将在浏览器中打开...")
    
    demo_app.launch(
        share=True,
        debug=False,
        show_error=True
    )
