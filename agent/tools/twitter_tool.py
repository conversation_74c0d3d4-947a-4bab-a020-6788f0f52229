"""
Twitter 分析工具 - 用于抓取和分析KOL推文
"""

from typing import Dict, Any, Optional, List
try:
    from llama_index.tools import BaseTool
except ImportError:
    # 备用基类
    class BaseTool:
        def __init__(self, name, description):
            self.name = name
            self.description = description

        def call(self, **kwargs):
            raise NotImplementedError
from modules.kol_scraper import KOLScraper
from modules.llm_analyzer import LLMAnalyzer
import json


class TwitterAnalysisTool(BaseTool):
    """Twitter 分析工具类"""
    
    def __init__(self):
        self.scraper = KOLScraper()
        self.llm_analyzer = LLMAnalyzer()
        super().__init__(
            name="twitter_analysis_tool",
            description="""
            分析KOL推文和社交媒体影响力的工具。
            
            功能包括：
            1. 分析KOL对特定币种的提及
            2. 获取推文互动数据
            3. 进行情绪分析
            4. 计算影响力评分
            
            输入参数：
            - action: 操作类型 (analyze_mentions, get_kol_tweets, sentiment_analysis)
            - username: KOL用户名
            - coin_symbol: 币种符号
            - coin_name: 币种名称
            - days: 分析天数 (默认7天)
            - tweet_text: 推文内容 (用于情绪分析)
            """
        )
    
    def call(self, **kwargs) -> str:
        """执行Twitter分析工具调用"""
        try:
            action = kwargs.get('action', 'analyze_mentions')
            username = kwargs.get('username', '')
            coin_symbol = kwargs.get('coin_symbol', '')
            coin_name = kwargs.get('coin_name', '')
            days = kwargs.get('days', 7)
            tweet_text = kwargs.get('tweet_text', '')
            
            if action == 'analyze_mentions':
                return self._analyze_kol_mentions(username, coin_symbol, coin_name, days)
            elif action == 'get_kol_tweets':
                return self._get_kol_tweets(username, days)
            elif action == 'sentiment_analysis':
                return self._analyze_sentiment(tweet_text)
            else:
                return json.dumps({
                    "error": f"不支持的操作: {action}",
                    "supported_actions": ["analyze_mentions", "get_kol_tweets", "sentiment_analysis"]
                })
                
        except Exception as e:
            return json.dumps({"error": f"Twitter分析工具执行失败: {str(e)}"})
    
    def _analyze_kol_mentions(self, username: str, coin_symbol: str, coin_name: str, days: int) -> str:
        """分析KOL对币种的提及"""
        if not username or not coin_symbol:
            return json.dumps({"error": "请提供KOL用户名和币种符号"})
        
        try:
            kol_data = self.scraper.analyze_kol_mentions(username, coin_symbol, coin_name, days)
            return json.dumps({
                "success": True,
                "kol_data": kol_data,
                "message": f"分析了 @{username} 对 {coin_symbol} 的提及情况"
            })
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"KOL提及分析失败: {str(e)}"
            })
    
    def _get_kol_tweets(self, username: str, days: int) -> str:
        """获取KOL推文"""
        if not username:
            return json.dumps({"error": "请提供KOL用户名"})
        
        try:
            tweets = self.scraper.get_user_tweets(username, days)
            return json.dumps({
                "success": True,
                "tweets": tweets[:10],  # 限制返回数量
                "total_tweets": len(tweets),
                "message": f"获取了 @{username} 最近 {days} 天的推文"
            })
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"推文获取失败: {str(e)}"
            })
    
    def _analyze_sentiment(self, tweet_text: str) -> str:
        """分析推文情绪"""
        if not tweet_text:
            return json.dumps({"error": "请提供推文内容"})
        
        try:
            sentiment = self.llm_analyzer.analyze_sentiment(tweet_text)
            return json.dumps({
                "success": True,
                "sentiment": sentiment,
                "message": "完成情绪分析"
            })
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"情绪分析失败: {str(e)}"
            })
