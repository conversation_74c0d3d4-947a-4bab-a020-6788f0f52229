"""
KOL 分析工具 - 用于综合分析和排名
"""

from typing import Dict, Any, Optional, List
try:
    from llama_index.tools import BaseTool
except ImportError:
    # 备用基类
    class BaseTool:
        def __init__(self, name, description):
            self.name = name
            self.description = description

        def call(self, **kwargs):
            raise NotImplementedError

from modules.analyzer import ShillRankAnalyzer
import json


class KOLAnalysisTool(BaseTool):
    """KOL 综合分析工具类"""
    
    def __init__(self):
        self.analyzer = ShillRankAnalyzer()
        super().__init__(
            name="kol_analysis_tool",
            description="""
            KOL影响力综合分析工具。
            
            功能包括：
            1. 分析币种的KOL影响力排名
            2. 获取单个KOL的详细分析
            3. 生成影响力报告
            4. 比较多个KOL的表现
            
            输入参数：
            - action: 操作类型 (analyze_coin, get_kol_detail, generate_report, compare_kols)
            - coin_query: 币种名称或符号
            - username: KOL用户名
            - days: 分析天数 (默认7天)
            - usernames: KOL用户名列表 (用于比较)
            """
        )
    
    def call(self, **kwargs) -> str:
        """执行KOL分析工具调用"""
        try:
            action = kwargs.get('action', 'analyze_coin')
            coin_query = kwargs.get('coin_query', '')
            username = kwargs.get('username', '')
            days = kwargs.get('days', 7)
            usernames = kwargs.get('usernames', [])
            
            if action == 'analyze_coin':
                return self._analyze_coin_influence(coin_query, days)
            elif action == 'get_kol_detail':
                return self._get_kol_detail(username, coin_query, days)
            elif action == 'generate_report':
                return self._generate_analysis_report(coin_query, days)
            elif action == 'compare_kols':
                return self._compare_kols(usernames, coin_query, days)
            else:
                return json.dumps({
                    "error": f"不支持的操作: {action}",
                    "supported_actions": ["analyze_coin", "get_kol_detail", "generate_report", "compare_kols"]
                })
                
        except Exception as e:
            return json.dumps({"error": f"KOL分析工具执行失败: {str(e)}"})
    
    def _analyze_coin_influence(self, coin_query: str, days: int) -> str:
        """分析币种的KOL影响力"""
        if not coin_query:
            return json.dumps({"error": "请提供币种名称或符号"})
        
        try:
            result = self.analyzer.analyze_coin_influence(coin_query, days)
            
            if 'error' in result:
                return json.dumps({
                    "success": False,
                    "error": result['error']
                })
            
            # 简化返回数据，只包含关键信息
            simplified_result = {
                "coin_info": result['coin_info'],
                "current_price": result.get('current_price', {}),
                "top_kols": result['kol_rankings'][:5],  # 只返回前5名
                "summary": result['summary'],
                "total_active_kols": len(result['kol_rankings']),
                "analysis_date": result['analysis_date']
            }
            
            return json.dumps({
                "success": True,
                "analysis_result": simplified_result,
                "message": f"完成了 {coin_query} 的KOL影响力分析"
            })
            
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"币种影响力分析失败: {str(e)}"
            })
    
    def _get_kol_detail(self, username: str, coin_query: str, days: int) -> str:
        """获取单个KOL的详细分析"""
        if not username or not coin_query:
            return json.dumps({"error": "请提供KOL用户名和币种名称"})
        
        try:
            kol_detail = self.analyzer.get_kol_detail(username, coin_query, days)
            
            if not kol_detail:
                return json.dumps({
                    "success": False,
                    "error": f"无法获取 @{username} 对 {coin_query} 的分析数据"
                })
            
            return json.dumps({
                "success": True,
                "kol_detail": kol_detail,
                "message": f"获取了 @{username} 对 {coin_query} 的详细分析"
            })
            
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"KOL详细分析失败: {str(e)}"
            })
    
    def _generate_analysis_report(self, coin_query: str, days: int) -> str:
        """生成分析报告"""
        if not coin_query:
            return json.dumps({"error": "请提供币种名称或符号"})
        
        try:
            result = self.analyzer.analyze_coin_influence(coin_query, days)
            
            if 'error' in result:
                return json.dumps({
                    "success": False,
                    "error": result['error']
                })
            
            # 生成详细报告
            report = self._format_analysis_report(result)
            
            return json.dumps({
                "success": True,
                "report": report,
                "message": f"生成了 {coin_query} 的详细分析报告"
            })
            
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"报告生成失败: {str(e)}"
            })
    
    def _compare_kols(self, usernames: List[str], coin_query: str, days: int) -> str:
        """比较多个KOL的表现"""
        if not usernames or not coin_query:
            return json.dumps({"error": "请提供KOL用户名列表和币种名称"})
        
        try:
            comparison_data = []
            
            for username in usernames[:5]:  # 限制比较数量
                kol_detail = self.analyzer.get_kol_detail(username, coin_query, days)
                if kol_detail:
                    comparison_data.append(kol_detail)
            
            return json.dumps({
                "success": True,
                "comparison_data": comparison_data,
                "message": f"比较了 {len(comparison_data)} 个KOL对 {coin_query} 的影响"
            })
            
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"KOL比较失败: {str(e)}"
            })
    
    def _format_analysis_report(self, result: Dict) -> str:
        """格式化分析报告"""
        coin_info = result['coin_info']
        kol_rankings = result['kol_rankings']
        
        report = f"""
# {coin_info['name']} ({coin_info['symbol'].upper()}) KOL影响力分析报告

## 基本信息
- 币种: {coin_info['name']} ({coin_info['symbol'].upper()})
- 分析天数: {result['days_analyzed']} 天
- 活跃KOL数量: {len(kol_rankings)} 位

## 排行榜前5名
"""
        
        for i, kol in enumerate(kol_rankings[:5], 1):
            report += f"""
### {i}. {kol['display_name']} (@{kol['username']})
- 综合评分: {kol['total_score']:.1f}
- 提及次数: {kol['coin_mentions']} 次
- 平均互动: {kol['avg_interactions']:.0f}
"""
            if 'avg_price_impact' in kol:
                report += f"- 平均价格影响: {kol['avg_price_impact']:.2f}%\n"
        
        report += f"\n## AI总结\n{result['summary']}"
        
        return report
